server:
  port: 8080
  servlet:
    context-path: /api
  # 生产环境优化配置
  tomcat:
    max-threads: 200
    min-spare-threads: 10
    connection-timeout: 20000
    accept-count: 100

spring:
  application:
    name: UserManager

  # 生产环境数据库配置
  datasource:
    url: ${SPRING_DATASOURCE_URL:*******************************************************************************************************************************************************}
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: ${SPRING_DATASOURCE_USERNAME:root}
    password: ${SPRING_DATASOURCE_PASSWORD:Cll@123?}

    # 生产环境连接池优化
    hikari:
      max-lifetime: 1800000 # 30分钟
      validation-timeout: 5000 # 5秒
      connection-timeout: 30000 # 30秒
      idle-timeout: 600000 # 10分钟
      maximum-pool-size: 50
      minimum-idle: 10
      connection-test-query: SELECT 1
      leak-detection-threshold: 60000 # 1分钟

  # 数据库初始化配置（生产环境建议关闭）
  sql:
    init:
      mode: never # 生产环境不自动执行SQL脚本

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate # 生产环境只验证，不自动创建表
    show-sql: false # 生产环境不显示SQL
    properties:
      hibernate:
        format_sql: false
        use_sql_comments: false

# MyBatis 配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: false
    auto-mapping-behavior: partial

# JWT配置
jwt:
  secret: ${JWT_SECRET:mySecretKey1234567890123456789012345678901234567890123456789012345678901234567890}
  expiration: 86400000  # 24小时
  header: X-Auth-Token

# 生产环境日志配置
logging:
  level:
    root: INFO
    com.cet.aug.usermanager: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/usermanager.log
    max-size: 100MB
    max-history: 30
    total-size-cap: 1GB

# 应用自定义配置
app:
  name: UserManager API
  version: 1.0.0
  description: User Management API with activation code login system

  # 激活码配置
  activation:
    code-length: 16
    expiry-days: 365

  # VIP配置
  vip:
    default-score: 1000
    default-day-score: 100.0
    default-power: 5
    default-product: premium

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
  health:
    db:
      enabled: true
    diskspace:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# 安全配置
security:
  # CORS配置
  cors:
    allowed-origins: "*"
    allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
    allowed-headers: "*"
    allow-credentials: true
    max-age: 3600
