# UserManager API 使用示例

## 基础信息

- **基础URL**: `http://localhost:8080/api`
- **Content-Type**: `application/json`
- **认证方式**: JWT <PERSON> (Header: `X-Auth-Token`)

## API接口示例

### 1. 健康检查

**请求**:
```http
GET /api/health
```

**响应**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "status": "UP",
    "application": "UserManager API",
    "version": "1.0.0",
    "timestamp": "2025-08-20T09:32:58",
    "message": "Service is running normally"
  }
}
```

### 2. 激活码登录

**请求**:
```http
POST /api/users/card-login
Content-Type: application/json

{
  "card": "test123456789",
  "agent": "main"
}
```

**成功响应**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": "user_test1234",
    "activationCode": "test123456789",
    "token": "eyJhbGciOiJIUzUxMiJ9.****************************************************************************.LON1F...",
    "vip": {
      "id": 1,
      "expire_at": 1787189578407,
      "day_score": 200.0,
      "refresh_at": 1755653578407,
      "power": 8,
      "product": "premium",
      "score": 2000,
      "score_used": 0
    },
    "agent": "main",
    "status": "active",
    "createdAt": "2025-08-20T09:32:58",
    "lastLoginAt": "2025-08-20T09:33:19"
  }
}
```

**错误响应** (空激活码):
```json
{
  "code": 400,
  "msg": "激活码不能为空",
  "data": {
    "card": "激活码不能为空"
  }
}
```

### 3. 获取用户信息

**请求**:
```http
POST /api/users/whoami
X-Auth-Token: eyJhbGciOiJIUzUxMiJ9.****************************************************************************.LON1F...
```

**响应**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": "user_test1234",
    "activationCode": "test123456789",
    "token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ1c2VyX3Rlc3QxMjM0IiwiaWF0IjoxNzU1NjUzNjc0LCJleHAiOjE3NTU3NDAwNzR9.Zs5Cy...",
    "vip": {
      "id": 1,
      "expire_at": 1787189578407,
      "day_score": 200.0,
      "refresh_at": 1755653578407,
      "power": 8,
      "product": "premium",
      "score": 2000,
      "score_used": 0
    },
    "agent": "main",
    "status": "active",
    "createdAt": "2025-08-20T09:32:58",
    "lastLoginAt": "2025-08-20T09:33:19"
  }
}
```

### 4. 用户登出

**请求**:
```http
POST /api/users/logout
X-Auth-Token: eyJhbGciOiJIUzUxMiJ9.****************************************************************************.LON1F...
```

**响应**:
```json
{
  "code": 0,
  "msg": "success",
  "data": "登出成功"
}
```

## 使用流程示例

### 完整的用户登录流程

1. **用户登录**:
```bash
curl -X POST http://localhost:8080/api/users/card-login \
  -H "Content-Type: application/json" \
  -d '{"card":"test123456789","agent":"main"}'
```

2. **提取Token** (从响应中获取token字段)

3. **获取用户信息**:
```bash
curl -X POST http://localhost:8080/api/users/whoami \
  -H "X-Auth-Token: YOUR_JWT_TOKEN_HERE"
```

4. **用户登出**:
```bash
curl -X POST http://localhost:8080/api/users/logout \
  -H "X-Auth-Token: YOUR_JWT_TOKEN_HERE"
```

## PowerShell 示例

### 登录并获取Token
```powershell
$response = Invoke-WebRequest -Uri "http://localhost:8080/api/users/card-login" `
  -Method POST `
  -ContentType "application/json" `
  -Body '{"card":"test123456789","agent":"main"}' `
  -UseBasicParsing

$json = $response.Content | ConvertFrom-Json
$token = $json.data.token
Write-Host "Token: $token"
```

### 使用Token获取用户信息
```powershell
$response = Invoke-WebRequest -Uri "http://localhost:8080/api/users/whoami" `
  -Method POST `
  -Headers @{"X-Auth-Token"=$token} `
  -UseBasicParsing

$userInfo = $response.Content | ConvertFrom-Json
Write-Host "User ID: $($userInfo.data.id)"
Write-Host "VIP Status: $($userInfo.data.vip.product)"
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1 | 一般错误 |
| 400 | 参数验证错误 |
| 401 | 认证失败 |
| 403 | 权限不足 |
| 404 | 资源未找到 |
| 500 | 服务器内部错误 |

## 测试数据

系统预置了以下测试数据：

| 激活码 | 代理 | VIP类型 | 权限等级 | 积分 |
|--------|------|---------|----------|------|
| test123456789 | main | premium | 8 | 2000 |
| demo987654321 | main | basic | 5 | 1000 |

## 注意事项

1. **JWT Token有效期**: 24小时，过期后需要重新登录
2. **请求头**: 认证接口需要在请求头中包含 `X-Auth-Token`
3. **Content-Type**: POST请求需要设置 `Content-Type: application/json`
4. **时间戳**: 响应中的时间戳为毫秒级Unix时间戳
5. **用户ID生成**: 新用户的ID会自动生成，格式为 `user_` + 12位随机字符

## 开发调试

### H2数据库控制台
- **URL**: http://localhost:8080/api/h2-console
- **JDBC URL**: jdbc:h2:mem:testdb
- **用户名**: sa
- **密码**: password

### 日志级别
开发环境下，应用会输出详细的SQL日志和调试信息，便于开发调试。
