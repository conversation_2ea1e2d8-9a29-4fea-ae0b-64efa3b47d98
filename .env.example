# UserManager 环境配置文件模板
# 复制此文件为 .env 并根据实际环境修改配置

# ===========================================
# MySQL 数据库配置
# ===========================================
MYSQL_ROOT_PASSWORD=Cll@123?
MYSQL_DATABASE=model
MYSQL_USER=usermanager
MYSQL_PASSWORD=usermanager123
MYSQL_PORT=3306

# ===========================================
# Spring Boot 应用配置
# ===========================================
SPRING_PROFILES_ACTIVE=prod

# JWT密钥配置（生产环境请使用更复杂的密钥）
JWT_SECRET=mySecretKey1234567890123456789012345678901234567890123456789012345678901234567890

# ===========================================
# 端口配置
# ===========================================
FRONTEND_PORT=80
BACKEND_PORT=8080

# ===========================================
# 可选配置
# ===========================================

# 时区设置
TZ=Asia/Shanghai

# 日志级别
LOG_LEVEL=INFO

# 数据库连接池配置
DB_MAX_POOL_SIZE=20
DB_MIN_IDLE=10

# 应用性能配置
JVM_OPTS=-Xms512m -Xmx1024m

# Nginx配置
NGINX_WORKER_PROCESSES=auto
NGINX_WORKER_CONNECTIONS=1024

# ===========================================
# 安全配置（生产环境必须修改）
# ===========================================

# 请在生产环境中修改以下默认值：
# 1. 所有密码都应该使用强密码
# 2. JWT_SECRET 应该是随机生成的256位密钥
# 3. 考虑启用HTTPS和其他安全措施

# 生成强密码的命令示例：
# openssl rand -base64 32

# 生成JWT密钥的命令示例：
# openssl rand -base64 64
