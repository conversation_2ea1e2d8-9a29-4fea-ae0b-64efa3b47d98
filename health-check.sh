#!/bin/bash

# UserManager 健康检查脚本
# 用于监控系统健康状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
COMPOSE_FILE="docker-compose.yml"
FRONTEND_URL="http://localhost"
BACKEND_URL="http://localhost:8080"
MYSQL_CONTAINER="usermanager-mysql"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker服务
check_docker() {
    log_info "检查Docker服务状态..."
    
    if ! systemctl is-active --quiet docker; then
        log_error "Docker服务未运行"
        return 1
    fi
    
    log_success "Docker服务正常"
    return 0
}

# 检查容器状态
check_containers() {
    log_info "检查容器状态..."
    
    local containers=$(docker-compose -f "$COMPOSE_FILE" ps --services 2>/dev/null || echo "")
    
    if [ -z "$containers" ]; then
        log_error "未找到运行中的容器"
        return 1
    fi
    
    local all_healthy=true
    
    for container in $containers; do
        local status=$(docker-compose -f "$COMPOSE_FILE" ps -q $container 2>/dev/null | xargs docker inspect --format='{{.State.Status}}' 2>/dev/null || echo "not_found")
        
        case $status in
            "running")
                log_success "$container: 运行中"
                ;;
            "exited")
                log_error "$container: 已退出"
                all_healthy=false
                ;;
            "not_found")
                log_error "$container: 未找到"
                all_healthy=false
                ;;
            *)
                log_warning "$container: $status"
                all_healthy=false
                ;;
        esac
    done
    
    if [ "$all_healthy" = true ]; then
        return 0
    else
        return 1
    fi
}

# 检查网络连通性
check_network() {
    log_info "检查网络连通性..."
    
    local ports=(80 8080 3306)
    local all_accessible=true
    
    for port in "${ports[@]}"; do
        if nc -z localhost $port 2>/dev/null; then
            log_success "端口 $port: 可访问"
        else
            log_error "端口 $port: 不可访问"
            all_accessible=false
        fi
    done
    
    if [ "$all_accessible" = true ]; then
        return 0
    else
        return 1
    fi
}

# 检查前端服务
check_frontend() {
    log_info "检查前端服务..."
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$FRONTEND_URL/health" 2>/dev/null || echo "000")
    
    if [ "$response" = "200" ]; then
        log_success "前端服务正常 (HTTP $response)"
        return 0
    else
        log_error "前端服务异常 (HTTP $response)"
        return 1
    fi
}

# 检查后端服务
check_backend() {
    log_info "检查后端服务..."
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$BACKEND_URL/api/health" 2>/dev/null || echo "000")
    
    if [ "$response" = "200" ]; then
        log_success "后端服务正常 (HTTP $response)"
        
        # 检查API响应内容
        local api_response=$(curl -s "$BACKEND_URL/api/health" 2>/dev/null || echo "")
        if [[ "$api_response" == *"UP"* ]] || [[ "$api_response" == *"healthy"* ]]; then
            log_success "后端API响应正常"
            return 0
        else
            log_warning "后端API响应异常: $api_response"
            return 1
        fi
    else
        log_error "后端服务异常 (HTTP $response)"
        return 1
    fi
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    local db_status=$(docker-compose -f "$COMPOSE_FILE" exec -T mysql mysqladmin ping -h localhost --silent 2>/dev/null && echo "OK" || echo "FAIL")
    
    if [ "$db_status" = "OK" ]; then
        log_success "数据库连接正常"
        
        # 检查数据库表
        local table_count=$(docker-compose -f "$COMPOSE_FILE" exec -T mysql mysql -u root -p${MYSQL_ROOT_PASSWORD:-Cll@123?} -e "USE model; SHOW TABLES;" 2>/dev/null | wc -l || echo "0")
        
        if [ "$table_count" -gt 1 ]; then
            log_success "数据库表结构正常 ($((table_count-1)) 个表)"
        else
            log_warning "数据库表可能未正确初始化"
        fi
        
        return 0
    else
        log_error "数据库连接失败"
        return 1
    fi
}

# 检查系统资源
check_resources() {
    log_info "检查系统资源..."
    
    # 检查磁盘空间
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 80 ]; then
        log_success "磁盘空间充足 (已使用 ${disk_usage}%)"
    elif [ "$disk_usage" -lt 90 ]; then
        log_warning "磁盘空间紧张 (已使用 ${disk_usage}%)"
    else
        log_error "磁盘空间不足 (已使用 ${disk_usage}%)"
    fi
    
    # 检查内存使用
    local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$mem_usage" -lt 80 ]; then
        log_success "内存使用正常 (已使用 ${mem_usage}%)"
    elif [ "$mem_usage" -lt 90 ]; then
        log_warning "内存使用较高 (已使用 ${mem_usage}%)"
    else
        log_error "内存使用过高 (已使用 ${mem_usage}%)"
    fi
    
    # 检查CPU负载
    local cpu_load=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores=$(nproc)
    local load_percentage=$(echo "$cpu_load $cpu_cores" | awk '{printf "%.0f", ($1/$2)*100}')
    
    if [ "$load_percentage" -lt 70 ]; then
        log_success "CPU负载正常 (负载 ${cpu_load}, ${load_percentage}%)"
    elif [ "$load_percentage" -lt 90 ]; then
        log_warning "CPU负载较高 (负载 ${cpu_load}, ${load_percentage}%)"
    else
        log_error "CPU负载过高 (负载 ${cpu_load}, ${load_percentage}%)"
    fi
}

# 生成健康报告
generate_report() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local report_file="health-report-$(date '+%Y%m%d-%H%M%S').txt"
    
    {
        echo "UserManager 健康检查报告"
        echo "生成时间: $timestamp"
        echo "=================================="
        echo ""
        
        echo "容器状态:"
        docker-compose -f "$COMPOSE_FILE" ps 2>/dev/null || echo "无法获取容器状态"
        echo ""
        
        echo "系统资源:"
        echo "磁盘使用: $(df -h / | awk 'NR==2 {print $5}')"
        echo "内存使用: $(free -h | awk 'NR==2{printf "%s/%s (%.0f%%)", $3,$2,$3*100/$2}')"
        echo "CPU负载: $(uptime | awk -F'load average:' '{print $2}')"
        echo ""
        
        echo "服务检查:"
        echo "前端: $(curl -s -o /dev/null -w "%{http_code}" "$FRONTEND_URL/health" 2>/dev/null || echo "无响应")"
        echo "后端: $(curl -s -o /dev/null -w "%{http_code}" "$BACKEND_URL/api/health" 2>/dev/null || echo "无响应")"
        echo "数据库: $(docker-compose -f "$COMPOSE_FILE" exec -T mysql mysqladmin ping -h localhost --silent 2>/dev/null && echo "正常" || echo "异常")"
        
    } > "$report_file"
    
    log_info "健康报告已生成: $report_file"
}

# 显示帮助信息
show_help() {
    echo "UserManager 健康检查脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --docker         仅检查Docker服务"
    echo "  --containers     仅检查容器状态"
    echo "  --network        仅检查网络连通性"
    echo "  --frontend       仅检查前端服务"
    echo "  --backend        仅检查后端服务"
    echo "  --database       仅检查数据库"
    echo "  --resources      仅检查系统资源"
    echo "  --report         生成详细报告"
    echo "  --help           显示此帮助信息"
    echo ""
}

# 主函数
main() {
    case "${1:-all}" in
        --docker)
            check_docker
            ;;
        --containers)
            check_containers
            ;;
        --network)
            check_network
            ;;
        --frontend)
            check_frontend
            ;;
        --backend)
            check_backend
            ;;
        --database)
            check_database
            ;;
        --resources)
            check_resources
            ;;
        --report)
            generate_report
            ;;
        --help)
            show_help
            ;;
        all|*)
            log_info "开始全面健康检查..."
            echo ""
            
            local checks=(
                "check_docker"
                "check_containers" 
                "check_network"
                "check_frontend"
                "check_backend"
                "check_database"
                "check_resources"
            )
            
            local failed_checks=0
            
            for check in "${checks[@]}"; do
                if ! $check; then
                    ((failed_checks++))
                fi
                echo ""
            done
            
            echo "=================================="
            if [ $failed_checks -eq 0 ]; then
                log_success "所有检查通过！系统运行正常。"
            else
                log_warning "发现 $failed_checks 个问题，请检查上述错误信息。"
            fi
            
            echo ""
            log_info "如需详细报告，请运行: $0 --report"
            ;;
    esac
}

# 执行主函数
main "$@"
