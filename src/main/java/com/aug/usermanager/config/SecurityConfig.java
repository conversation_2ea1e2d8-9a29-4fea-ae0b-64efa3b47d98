package com.aug.usermanager.config;

import com.aug.usermanager.security.JwtAuthenticationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * Spring Security配置
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {
    
    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.cors().and().csrf().disable()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .authorizeRequests()
                // 公开接口
                .antMatchers("/health").permitAll()
                .antMatchers("/users/card-login").permitAll()
                .antMatchers("/admin/login").permitAll()    // 管理员登录
                .antMatchers("/h2-console/**").permitAll()  // H2控制台
                .antMatchers("/actuator/**").permitAll()    // Spring Boot Actuator
                .antMatchers("/error").permitAll()         // Spring Boot 错误页面
                .antMatchers("/favicon.ico").permitAll()   // 网站图标
                .antMatchers("/users/whoami").permitAll()
                .antMatchers("/users/logout").permitAll()
                // 需要认证的接口
                .antMatchers("/users/generate-activation-code").authenticated()  // 激活码生成接口
                .antMatchers("/admin/**").authenticated()   // 管理员接口
                // 其他所有请求都需要认证
                .anyRequest().authenticated()
            .and()
            .headers().frameOptions().disable(); // 允许H2控制台使用iframe

        // 添加JWT过滤器
        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
    }
}
