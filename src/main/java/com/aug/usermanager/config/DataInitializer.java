package com.aug.usermanager.config;

import com.aug.usermanager.entity.User;
import com.aug.usermanager.entity.VipInfo;
import com.aug.usermanager.mapper.UserMapper;
import com.aug.usermanager.mapper.VipInfoMapper;
import com.aug.usermanager.service.UserCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 数据初始化器
 * 在应用启动时初始化测试数据
 */
@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private VipInfoMapper vipInfoMapper;

    @Autowired
    private UserCacheService userCacheService;

    @Override
    public void run(String... args) throws Exception {
        // 清理旧的缓存数据，避免序列化兼容性问题
        System.out.println("清理旧的缓存数据...");
        try {
            userCacheService.clearAllUserCache();
        } catch (Exception e) {
            System.err.println("清理缓存失败，但不影响应用启动: " + e.getMessage());
        }

        initializeTestData();
    }

    /**
     * 初始化测试数据
     */
    private void initializeTestData() {
        try {
            System.out.println("开始检查和初始化测试数据...");

            // 检查并创建第一个测试用户 - Premium用户
            if (userMapper.findByActivationCode("test123456789") == null) {
                createTestUser("test123456789", "main", "premium", 8, 2000, 200.0f);
                System.out.println("创建第一个测试用户: test123456789 (premium)");
            } else {
                System.out.println("第一个测试用户已存在: test123456789");
            }

            // 检查并创建第二个测试用户 - Basic用户
            if (userMapper.findByActivationCode("demo987654321") == null) {
                createTestUser("demo987654321", "main", "basic", 5, 1000, 100.0f);
                System.out.println("创建第二个测试用户: demo987654321 (basic)");
            } else {
                System.out.println("第二个测试用户已存在: demo987654321");
            }

            System.out.println("测试数据初始化完成！");
            System.out.println("可用的测试激活码:");
            System.out.println("1. test123456789 (premium用户, 权限等级8, 积分2000)");
            System.out.println("2. demo987654321 (basic用户, 权限等级5, 积分1000)");
            System.out.println("代理标识: main");

        } catch (Exception e) {
            System.err.println("初始化测试数据失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建测试用户
     */
    private void createTestUser(String activationCode, String agent, String product, 
                               int power, int score, float dayScore) {
        try {
            // 创建VIP信息
            VipInfo vipInfo = new VipInfo();
            long currentTime = System.currentTimeMillis();
            long expireTime = currentTime + (365L * 24 * 60 * 60 * 1000); // 1年后过期
            
            vipInfo.setExpireAt(expireTime);
            vipInfo.setDayScore(dayScore);
            vipInfo.setRefreshAt(currentTime);
            vipInfo.setPower(power);
            vipInfo.setProduct(product);
            vipInfo.setScore(score);
            vipInfo.setScoreUsed(0);

            // 插入VIP信息
            vipInfoMapper.insert(vipInfo);

            // 创建用户
            User user = new User();
            user.setUserId(generateUserId(activationCode));
            user.setActivationCode(activationCode);
            user.setAgent(agent);
            user.setStatus("active");
            user.setVipInfoId(vipInfo.getId());
            user.setCreatedAt(LocalDateTime.now());
            user.setLastLoginAt(LocalDateTime.now());

            // 插入用户
            userMapper.insert(user);

            System.out.println("创建测试用户成功: " + activationCode + " (" + product + ")");

        } catch (Exception e) {
            System.err.println("创建测试用户失败 [" + activationCode + "]: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 根据激活码生成用户ID
     */
    private String generateUserId(String activationCode) {
        if ("test123456789".equals(activationCode)) {
            return "user_test_premium";
        } else if ("demo987654321".equals(activationCode)) {
            return "user_demo_basic";
        } else {
            return "user_" + activationCode.substring(0, Math.min(8, activationCode.length()));
        }
    }
}
