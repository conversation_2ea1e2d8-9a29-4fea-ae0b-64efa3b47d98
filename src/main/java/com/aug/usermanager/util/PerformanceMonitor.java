package com.aug.usermanager.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能监控工具
 */
@Component
public class PerformanceMonitor {

    private static final Logger logger = LoggerFactory.getLogger(PerformanceMonitor.class);

    // 请求计数器
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong successRequests = new AtomicLong(0);
    private final AtomicLong failureRequests = new AtomicLong(0);

    // 响应时间统计
    private final AtomicLong totalResponseTime = new AtomicLong(0);
    private final AtomicLong maxResponseTime = new AtomicLong(0);
    private final AtomicLong minResponseTime = new AtomicLong(Long.MAX_VALUE);

    // 缓存命中率统计
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);

    // 方法级别的性能统计
    private final ConcurrentHashMap<String, MethodStats> methodStats = new ConcurrentHashMap<>();

    /**
     * 记录请求开始
     */
    public long recordRequestStart() {
        totalRequests.incrementAndGet();
        return System.currentTimeMillis();
    }

    /**
     * 记录请求成功
     */
    public void recordRequestSuccess(long startTime) {
        successRequests.incrementAndGet();
        recordResponseTime(startTime);
    }

    /**
     * 记录请求失败
     */
    public void recordRequestFailure(long startTime) {
        failureRequests.incrementAndGet();
        recordResponseTime(startTime);
    }

    /**
     * 记录响应时间
     */
    private void recordResponseTime(long startTime) {
        long responseTime = System.currentTimeMillis() - startTime;
        totalResponseTime.addAndGet(responseTime);
        
        // 更新最大响应时间
        long currentMax = maxResponseTime.get();
        while (responseTime > currentMax && !maxResponseTime.compareAndSet(currentMax, responseTime)) {
            currentMax = maxResponseTime.get();
        }
        
        // 更新最小响应时间
        long currentMin = minResponseTime.get();
        while (responseTime < currentMin && !minResponseTime.compareAndSet(currentMin, responseTime)) {
            currentMin = minResponseTime.get();
        }
    }

    /**
     * 记录缓存命中
     */
    public void recordCacheHit() {
        cacheHits.incrementAndGet();
    }

    /**
     * 记录缓存未命中
     */
    public void recordCacheMiss() {
        cacheMisses.incrementAndGet();
    }

    /**
     * 记录方法执行时间
     */
    public void recordMethodExecution(String methodName, long executionTime) {
        methodStats.computeIfAbsent(methodName, k -> new MethodStats()).addExecution(executionTime);
    }

    /**
     * 获取性能统计信息
     */
    public PerformanceStats getStats() {
        long total = totalRequests.get();
        long success = successRequests.get();
        long failure = failureRequests.get();
        long totalTime = totalResponseTime.get();
        long max = maxResponseTime.get();
        long min = minResponseTime.get() == Long.MAX_VALUE ? 0 : minResponseTime.get();
        
        long hits = cacheHits.get();
        long misses = cacheMisses.get();
        
        return new PerformanceStats(
            total, success, failure,
            total > 0 ? (double) totalTime / total : 0,
            max, min,
            hits + misses > 0 ? (double) hits / (hits + misses) * 100 : 0
        );
    }

    /**
     * 打印性能统计信息
     */
    public void printStats() {
        PerformanceStats stats = getStats();
        logger.info("=== 性能统计信息 ===");
        logger.info("总请求数: {}", stats.getTotalRequests());
        logger.info("成功请求数: {}", stats.getSuccessRequests());
        logger.info("失败请求数: {}", stats.getFailureRequests());
        logger.info("成功率: {:.2f}%", stats.getSuccessRate());
        logger.info("平均响应时间: {:.2f}ms", stats.getAverageResponseTime());
        logger.info("最大响应时间: {}ms", stats.getMaxResponseTime());
        logger.info("最小响应时间: {}ms", stats.getMinResponseTime());
        logger.info("缓存命中率: {:.2f}%", stats.getCacheHitRate());
        
        // 打印方法级别统计
        methodStats.forEach((methodName, methodStat) -> {
            logger.info("方法 {} - 调用次数: {}, 平均耗时: {:.2f}ms", 
                       methodName, methodStat.getCount(), methodStat.getAverageTime());
        });
    }

    /**
     * 重置统计信息
     */
    public void reset() {
        totalRequests.set(0);
        successRequests.set(0);
        failureRequests.set(0);
        totalResponseTime.set(0);
        maxResponseTime.set(0);
        minResponseTime.set(Long.MAX_VALUE);
        cacheHits.set(0);
        cacheMisses.set(0);
        methodStats.clear();
    }

    /**
     * 性能统计数据类
     */
    public static class PerformanceStats {
        private final long totalRequests;
        private final long successRequests;
        private final long failureRequests;
        private final double averageResponseTime;
        private final long maxResponseTime;
        private final long minResponseTime;
        private final double cacheHitRate;

        public PerformanceStats(long totalRequests, long successRequests, long failureRequests,
                               double averageResponseTime, long maxResponseTime, long minResponseTime,
                               double cacheHitRate) {
            this.totalRequests = totalRequests;
            this.successRequests = successRequests;
            this.failureRequests = failureRequests;
            this.averageResponseTime = averageResponseTime;
            this.maxResponseTime = maxResponseTime;
            this.minResponseTime = minResponseTime;
            this.cacheHitRate = cacheHitRate;
        }

        // Getters
        public long getTotalRequests() { return totalRequests; }
        public long getSuccessRequests() { return successRequests; }
        public long getFailureRequests() { return failureRequests; }
        public double getSuccessRate() { return totalRequests > 0 ? (double) successRequests / totalRequests * 100 : 0; }
        public double getAverageResponseTime() { return averageResponseTime; }
        public long getMaxResponseTime() { return maxResponseTime; }
        public long getMinResponseTime() { return minResponseTime; }
        public double getCacheHitRate() { return cacheHitRate; }
    }

    /**
     * 方法统计信息类
     */
    private static class MethodStats {
        private final AtomicLong count = new AtomicLong(0);
        private final AtomicLong totalTime = new AtomicLong(0);

        public void addExecution(long executionTime) {
            count.incrementAndGet();
            totalTime.addAndGet(executionTime);
        }

        public long getCount() {
            return count.get();
        }

        public double getAverageTime() {
            long c = count.get();
            return c > 0 ? (double) totalTime.get() / c : 0;
        }
    }
}
