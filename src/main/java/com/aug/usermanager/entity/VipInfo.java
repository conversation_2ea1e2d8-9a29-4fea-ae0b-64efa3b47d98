package com.aug.usermanager.entity;

import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * VIP会员信息实体类
 */
public class VipInfo {

    private Long id;

    /**
     * VIP过期时间戳（毫秒）
     */
    @JsonProperty("expire_at")
    private Long expireAt;

    /**
     * 每日可用积分额度
     */
    @JsonProperty("day_score")
    private Float dayScore;

    /**
     * 积分刷新时间戳
     */
    @JsonProperty("refresh_at")
    private Long refreshAt;

    /**
     * 用户权限等级（1-10）
     */
    private Integer power;

    /**
     * VIP产品类型（basic/premium/enterprise）
     */
    private String product;

    /**
     * 总可用积分
     */
    private Integer score;

    /**
     * 已使用积分
     */
    @JsonProperty("score_used")
    private Integer scoreUsed;
    
    // 默认构造函数
    public VipInfo() {}
    
    // 构造函数
    public VipInfo(Long expireAt, Float dayScore, Long refreshAt, Integer power, 
                   String product, Integer score, Integer scoreUsed) {
        this.expireAt = expireAt;
        this.dayScore = dayScore;
        this.refreshAt = refreshAt;
        this.power = power;
        this.product = product;
        this.score = score;
        this.scoreUsed = scoreUsed;
    }
    
    /**
     * 判断VIP是否有效
     */
    public boolean isValid() {
        if (expireAt == null || expireAt == 0) {
            return false;
        }
        return expireAt > System.currentTimeMillis();
    }
    
    /**
     * 获取剩余天数
     */
    public long getRemainingDays() {
        if (expireAt == null || expireAt == 0) {
            return 0;
        }
        long currentTime = System.currentTimeMillis();
        if (expireAt <= currentTime) {
            return 0;
        }
        return (expireAt - currentTime) / (24 * 60 * 60 * 1000);
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getExpireAt() {
        return expireAt;
    }
    
    public void setExpireAt(Long expireAt) {
        this.expireAt = expireAt;
    }
    
    public Float getDayScore() {
        return dayScore;
    }
    
    public void setDayScore(Float dayScore) {
        this.dayScore = dayScore;
    }
    
    public Long getRefreshAt() {
        return refreshAt;
    }
    
    public void setRefreshAt(Long refreshAt) {
        this.refreshAt = refreshAt;
    }
    
    public Integer getPower() {
        return power;
    }
    
    public void setPower(Integer power) {
        this.power = power;
    }
    
    public String getProduct() {
        return product;
    }
    
    public void setProduct(String product) {
        this.product = product;
    }
    
    public Integer getScore() {
        return score;
    }
    
    public void setScore(Integer score) {
        this.score = score;
    }
    
    public Integer getScoreUsed() {
        return scoreUsed;
    }
    
    public void setScoreUsed(Integer scoreUsed) {
        this.scoreUsed = scoreUsed;
    }
    
    @Override
    public String toString() {
        return "VipInfo{" +
                "id=" + id +
                ", expireAt=" + expireAt +
                ", dayScore=" + dayScore +
                ", refreshAt=" + refreshAt +
                ", power=" + power +
                ", product='" + product + '\'' +
                ", score=" + score +
                ", scoreUsed=" + scoreUsed +
                '}';
    }
}
