package com.aug.usermanager.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;

/**
 * 用户实体类
 */
public class User {

    private Long id;

    /**
     * 用户唯一标识符
     */
    private String userId;

    /**
     * 激活码
     */
    private String activationCode;

    /**
     * JWT认证令牌（不持久化到数据库）
     */
    private String token;

    /**
     * VIP会员信息
     */
    private VipInfo vip;

    /**
     * 关联的VIP信息ID
     */
    private Long vipInfoId;

    /**
     * 用户代理标识
     */
    private String agent;

    /**
     * 用户状态（active, inactive, banned）
     */
    private String status = "active";

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginAt;

    /**
     * 密码哈希（用于安全存储）
     */
    @JsonIgnore
    private String passwordHash;

    // 默认构造函数
    public User() {
        this.createdAt = LocalDateTime.now();
    }

    // 构造函数
    public User(String userId, String activationCode, String agent) {
        this();
        this.userId = userId;
        this.activationCode = activationCode;
        this.agent = agent;
    }

    /**
     * 判断用户是否为VIP
     */
    public boolean hasValidVip() {
        return vip != null && vip.isValid();
    }

    /**
     * 更新最后登录时间
     */
    public void updateLastLoginTime() {
        this.lastLoginAt = LocalDateTime.now();
    }

    /**
     * 检查用户是否处于活跃状态
     */
    public boolean isActive() {
        return "active".equals(this.status);
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @JsonProperty("id")
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getActivationCode() {
        return activationCode;
    }

    public void setActivationCode(String activationCode) {
        this.activationCode = activationCode;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public VipInfo getVip() {
        return vip;
    }

    public void setVip(VipInfo vip) {
        this.vip = vip;
    }

    public String getAgent() {
        return agent;
    }

    public void setAgent(String agent) {
        this.agent = agent;
    }

    public Long getVipInfoId() {
        return vipInfoId;
    }

    public void setVipInfoId(Long vipInfoId) {
        this.vipInfoId = vipInfoId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getLastLoginAt() {
        return lastLoginAt;
    }

    public void setLastLoginAt(LocalDateTime lastLoginAt) {
        this.lastLoginAt = lastLoginAt;
    }

    public String getPasswordHash() {
        return passwordHash;
    }

    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
    }

    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", userId='" + userId + '\'' +
                ", agent='" + agent + '\'' +
                ", status='" + status + '\'' +
                ", createdAt=" + createdAt +
                ", lastLoginAt=" + lastLoginAt +
                ", hasValidVip=" + hasValidVip() +
                '}';
    }
}
