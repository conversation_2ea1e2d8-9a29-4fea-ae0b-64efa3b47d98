package com.aug.usermanager.service;

import com.aug.usermanager.dto.ActivationCodeRequest;
import com.aug.usermanager.dto.ActivationCodeResponse;
import com.aug.usermanager.dto.LoginRequest;
import com.aug.usermanager.dto.UserStatistics;
import com.aug.usermanager.entity.User;
import com.aug.usermanager.entity.VipInfo;
import com.aug.usermanager.mapper.UserMapper;
import com.aug.usermanager.mapper.VipInfoMapper;
import com.aug.usermanager.security.JwtTokenProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 用户服务类
 */
@Service
@Transactional
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private VipInfoMapper vipInfoMapper;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private UserCacheService userCacheService;

    @Autowired
    private AsyncUserService asyncUserService;

    @Autowired
    private com.aug.usermanager.util.PerformanceMonitor performanceMonitor;

    /**
     * 激活码登录 - 优化版本（支持缓存和异步处理）
     */
    public User cardLogin(LoginRequest loginRequest) {
        long startTime = performanceMonitor.recordRequestStart();

        try {
            String activationCode = loginRequest.getCard().trim();
            String agent = loginRequest.getAgent();

        if (activationCode.isEmpty()) {
            throw new IllegalArgumentException("激活码不能为空");
        }

        // 1. 首先检查是否已知该激活码不存在（避免重复查询）
        if (userCacheService.isActivationCodeNotFound(activationCode)) {
            logger.debug("激活码 {} 已被标记为不存在，直接返回失败", activationCode);
            throw new IllegalArgumentException("激活码不存在，登录失败");
        }

        // 2. 尝试从缓存获取用户信息
        User user = userCacheService.getUserByActivationCode(activationCode);

        // 3. 缓存未命中，从数据库查询
        if (user == null) {
            performanceMonitor.recordCacheMiss();
            logger.debug("缓存未命中，从数据库查询激活码: {}", activationCode);
            user = userMapper.findByActivationCode(activationCode);

            if (user == null) {
                // 缓存激活码不存在的结果，避免重复查询
                userCacheService.cacheActivationCodeNotFound(activationCode);
                logger.warn("激活码不存在: {}", activationCode);
                throw new IllegalArgumentException("激活码不存在，登录失败");
            }

            // 将查询到的用户信息缓存
            userCacheService.cacheUserByActivationCode(activationCode, user);
            userCacheService.cacheUserById(user.getUserId(), user);
            logger.debug("用户信息已缓存: {}", user.getUserId());
        } else {
            performanceMonitor.recordCacheHit();
            logger.debug("从缓存获取到用户信息: {}", user.getUserId());
        }

        // 4. 检查代理标识
        if (!agent.equals(user.getAgent())) {
            logger.warn("代理标识不匹配 - 期望: {}, 实际: {}, 用户: {}", agent, user.getAgent(), user.getUserId());
            throw new IllegalArgumentException("代理标识不匹配");
        }

        // 5. 检查用户状态
        if (!user.isActive()) {
            logger.warn("用户账户已被禁用: {}", user.getUserId());
            throw new IllegalArgumentException("用户账户已被禁用");
        }

        // 6. 检查激活码是否过期
        if (user.getVip() != null && !user.getVip().isValid()) {
            long expireAt = user.getVip().getExpireAt();
            java.util.Date expireDate = new java.util.Date(expireAt);
            logger.warn("激活码已过期 - 用户: {}, 过期时间: {}", user.getUserId(), expireDate);
            throw new IllegalArgumentException("激活码已过期，过期时间：" + expireDate.toString() + "，登录失败");
        }

        // 7. 生成JWT令牌（同步操作，必须立即返回）
        String token = jwtTokenProvider.generateToken(user.getUserId());
        user.setToken(token);

        // 8. 异步处理非关键操作（提高响应速度）
        asyncUserService.updateLastLoginTimeAsync(user.getUserId());
        asyncUserService.logLoginAsync(user.getUserId(), activationCode, agent, getClientIp());
        asyncUserService.incrementLoginCountAsync(user.getUserId());
        asyncUserService.preloadRelatedUsersAsync(agent);

            logger.info("用户登录成功: {}", user.getUserId());
            performanceMonitor.recordRequestSuccess(startTime);
            return user;

        } catch (Exception e) {
            performanceMonitor.recordRequestFailure(startTime);
            logger.error("用户登录失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 创建新用户
     */
    private User createNewUser(String activationCode, String agent) {
        VipInfo vipInfo = createDefaultVipInfo();
        vipInfoMapper.insert(vipInfo); // 先插入VipInfo以获取ID

        User user = new User(generateUserId(), activationCode, agent);
        user.setVipInfoId(vipInfo.getId());
        user.setVip(vipInfo);
        user.setLastLoginAt(LocalDateTime.now());
        userMapper.insert(user);

        return user;
    }

    /**
     * 生成用户ID
     */
    private String generateUserId() {
        String userId;
        do {
            userId = "user_" + UUID.randomUUID().toString().replace("-", "").substring(0, 12);
        } while (userMapper.findByUserId(userId) != null);
        return userId;
    }

    /**
     * 创建默认VIP信息
     */
    private VipInfo createDefaultVipInfo() {
        long currentTime = System.currentTimeMillis();
        long expireTime = currentTime + (365L * 24 * 60 * 60 * 1000); // 1年后过期

        VipInfo vipInfo = new VipInfo();
        vipInfo.setExpireAt(expireTime);
        vipInfo.setDayScore(100.0f);
        vipInfo.setRefreshAt(currentTime);
        vipInfo.setPower(5);
        vipInfo.setProduct("premium");
        vipInfo.setScore(1000);
        vipInfo.setScoreUsed(0);

        return vipInfo;
    }

    /**
     * 根据用户ID查找用户
     */
    public User findByUserId(String userId) {
        User user = userMapper.findByUserId(userId);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }
        return user;
    }

    /**
     * 获取用户信息（whoami）
     */
    public User getUserInfo(String userId) {
        User user = findByUserId(userId);
        String token = jwtTokenProvider.generateToken(user.getUserId());
        user.setToken(token);
        return user;
    }

    /**
     * 用户登出
     */
    public void logout(String userId) {
        findByUserId(userId);
    }

    /**
     * 验证激活码
     */
    public boolean validateActivationCode(String activationCode) {
        return activationCode != null &&
               !activationCode.trim().isEmpty() &&
               activationCode.trim().length() >= 8;
    }

    /**
     * 更新用户VIP信息
     */
    public User updateUserVip(String userId, VipInfo newVipInfo) {
        User user = findByUserId(userId);
        VipInfo existingVip = user.getVip();

        if (existingVip != null) {
            newVipInfo.setId(existingVip.getId());
            vipInfoMapper.update(newVipInfo);
            user.setVip(newVipInfo);
        } else {
            vipInfoMapper.insert(newVipInfo);
            user.setVipInfoId(newVipInfo.getId());
            userMapper.update(user);
            user.setVip(newVipInfo);
        }

        return user;
    }

    /**
     * 获取所有用户
     */
    public List<User> getAllUsers() {
        return userMapper.findAll();
    }

    /**
     * 删除用户
     */
    public void deleteUser(String userId) {
        User user = findByUserId(userId);
        if (user.getVipInfoId() != null) {
            vipInfoMapper.delete(user.getVipInfoId());
        }
        userMapper.delete(user.getId());
    }

    /**
     * 获取用户统计信息
     */
    public UserStatistics getUserStatistics() {
        List<User> allUsers = userMapper.findAll();
        long currentTime = System.currentTimeMillis();

        long totalUsers = allUsers.size();
        long activeUsers = allUsers.stream().mapToLong(user -> user.isActive() ? 1 : 0).sum();
        long inactiveUsers = totalUsers - activeUsers;

        long totalVipUsers = allUsers.stream().mapToLong(user -> user.getVip() != null ? 1 : 0).sum();
        long validVipUsers = allUsers.stream().mapToLong(user ->
            user.getVip() != null && user.getVip().isValid() ? 1 : 0).sum();
        long expiredVipUsers = allUsers.stream().mapToLong(user ->
            user.getVip() != null && !user.getVip().isValid() ? 1 : 0).sum();
        long nonVipUsers = totalUsers - totalVipUsers;

        long premiumUsers = allUsers.stream().mapToLong(user ->
            user.getVip() != null && "premium".equals(user.getVip().getProduct()) ? 1 : 0).sum();
        long basicUsers = allUsers.stream().mapToLong(user ->
            user.getVip() != null && "basic".equals(user.getVip().getProduct()) ? 1 : 0).sum();
        long enterpriseUsers = allUsers.stream().mapToLong(user ->
            user.getVip() != null && "enterprise".equals(user.getVip().getProduct()) ? 1 : 0).sum();

        return new UserStatistics(totalUsers, activeUsers, inactiveUsers,
                                totalVipUsers, validVipUsers, expiredVipUsers,
                                nonVipUsers, premiumUsers, basicUsers, enterpriseUsers);
    }

    /**
     * 生成激活码
     * 格式：ABCDE-12345-FGHIJ-67890-KLMNO
     */
    public ActivationCodeResponse generateActivationCode(ActivationCodeRequest request) {
        // 生成激活码
        String activationCode = generateFormattedActivationCode();

        // 确保激活码唯一性
        while (userMapper.findByActivationCode(activationCode) != null) {
            activationCode = generateFormattedActivationCode();
        }

        // 创建VIP信息
        VipInfo vipInfo = createVipInfoFromRequest(request);
        vipInfoMapper.insert(vipInfo);

        // 创建用户
        User user = new User(generateUserId(), activationCode, request.getAgent());
        user.setVipInfoId(vipInfo.getId());
        user.setVip(vipInfo);
        user.setLastLoginAt(LocalDateTime.now());
        userMapper.insert(user);

        return new ActivationCodeResponse(activationCode, user, vipInfo, request.getExpireDays());
    }

    /**
     * 生成格式化的激活码
     * 格式：ABCDE-12345-FGHIJ-67890-KLMNO
     */
    private String generateFormattedActivationCode() {
        Random random = new SecureRandom();
        StringBuilder sb = new StringBuilder();

        // 字符集：大写字母和数字
        String letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String numbers = "0123456789";

        // 第一组：5个大写字母
        for (int i = 0; i < 5; i++) {
            sb.append(letters.charAt(random.nextInt(letters.length())));
        }
        sb.append("-");

        // 第二组：5个数字
        for (int i = 0; i < 5; i++) {
            sb.append(numbers.charAt(random.nextInt(numbers.length())));
        }
        sb.append("-");

        // 第三组：5个大写字母
        for (int i = 0; i < 5; i++) {
            sb.append(letters.charAt(random.nextInt(letters.length())));
        }
        sb.append("-");

        // 第四组：5个数字
        for (int i = 0; i < 5; i++) {
            sb.append(numbers.charAt(random.nextInt(numbers.length())));
        }
        sb.append("-");

        // 第五组：5个大写字母
        for (int i = 0; i < 5; i++) {
            sb.append(letters.charAt(random.nextInt(letters.length())));
        }

        return sb.toString();
    }

    /**
     * 根据请求创建VIP信息
     */
    private VipInfo createVipInfoFromRequest(ActivationCodeRequest request) {
        long currentTime = System.currentTimeMillis();
        long expireTime = currentTime + (request.getExpireDays() * 24L * 60 * 60 * 1000);

        VipInfo vipInfo = new VipInfo();
        vipInfo.setExpireAt(expireTime);
        vipInfo.setDayScore(request.getDayScore());
        vipInfo.setRefreshAt(currentTime);
        vipInfo.setPower(request.getPower());
        vipInfo.setProduct(request.getProduct());
        vipInfo.setScore(request.getScore());
        vipInfo.setScoreUsed(0);

        return vipInfo;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String ip = request.getHeader("X-Forwarded-For");
                if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("Proxy-Client-IP");
                }
                if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("WL-Proxy-Client-IP");
                }
                if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getRemoteAddr();
                }
                return ip;
            }
        } catch (Exception e) {
            logger.warn("获取客户端IP失败", e);
        }
        return "unknown";
    }
}
