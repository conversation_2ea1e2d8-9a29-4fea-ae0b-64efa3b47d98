package com.aug.usermanager.service;

import com.aug.usermanager.entity.User;
import com.aug.usermanager.mapper.UserMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 异步用户服务
 * 处理非关键的异步操作，提高主流程响应速度
 */
@Service
public class AsyncUserService {

    private static final Logger logger = LoggerFactory.getLogger(AsyncUserService.class);

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserCacheService userCacheService;

    /**
     * 异步更新用户最后登录时间
     */
    @Async
    public void updateLastLoginTimeAsync(String userId) {
        try {
            User user = new User();
            user.setUserId(userId);
            user.setLastLoginAt(LocalDateTime.now());
            
            int updated = userMapper.update(user);
            if (updated > 0) {
                logger.debug("异步更新用户 {} 最后登录时间成功", userId);
                
                // 更新缓存中的用户信息
                User cachedUser = userCacheService.getUserById(userId);
                if (cachedUser != null) {
                    cachedUser.setLastLoginAt(LocalDateTime.now());
                    userCacheService.cacheUserById(userId, cachedUser);
                }
            } else {
                logger.warn("异步更新用户 {} 最后登录时间失败", userId);
            }
        } catch (Exception e) {
            logger.error("异步更新用户 {} 最后登录时间时发生异常", userId, e);
        }
    }

    /**
     * 异步记录登录日志
     */
    @Async
    public void logLoginAsync(String userId, String activationCode, String agent, String clientIp) {
        try {
            // 这里可以记录到专门的日志表或日志文件
            logger.info("用户登录记录 - 用户ID: {}, 激活码: {}, 代理: {}, IP: {}, 时间: {}", 
                       userId, activationCode, agent, clientIp, LocalDateTime.now());
            
            // 如果有专门的登录日志表，可以在这里插入记录
            // loginLogMapper.insert(new LoginLog(userId, activationCode, agent, clientIp, LocalDateTime.now()));
            
        } catch (Exception e) {
            logger.error("异步记录登录日志时发生异常 - 用户ID: {}", userId, e);
        }
    }

    /**
     * 异步统计用户登录次数
     */
    @Async
    public void incrementLoginCountAsync(String userId) {
        try {
            // 这里可以更新用户统计信息
            logger.debug("异步更新用户 {} 登录统计", userId);
            
            // 示例：更新Redis中的登录计数
            // redisTemplate.opsForValue().increment("user:login_count:" + userId);
            
        } catch (Exception e) {
            logger.error("异步更新用户 {} 登录统计时发生异常", userId, e);
        }
    }

    /**
     * 异步预热相关用户缓存
     */
    @Async
    public void preloadRelatedUsersAsync(String agent) {
        try {
            // 预加载同一代理下的其他用户到缓存
            // 这可以提高后续相同代理用户的登录速度
            logger.debug("异步预热代理 {} 相关用户缓存", agent);
            
            // 实现逻辑：查询同一代理下的活跃用户并加载到缓存
            // List<User> relatedUsers = userMapper.findActiveUsersByAgent(agent);
            // userCacheService.warmUpCache(relatedUsers);
            
        } catch (Exception e) {
            logger.error("异步预热代理 {} 相关用户缓存时发生异常", agent, e);
        }
    }
}
