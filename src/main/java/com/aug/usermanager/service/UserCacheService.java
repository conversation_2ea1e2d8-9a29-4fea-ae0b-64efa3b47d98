package com.aug.usermanager.service;

import com.aug.usermanager.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 用户缓存服务
 */
@Service
public class UserCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String USER_CACHE_PREFIX = "user:";
    private static final String ACTIVATION_CODE_CACHE_PREFIX = "activation_code:";
    private static final long CACHE_EXPIRE_TIME = 24; // 24小时

    /**
     * 缓存用户信息（通过激活码）
     */
    public void cacheUserByActivationCode(String activationCode, User user) {
        String key = ACTIVATION_CODE_CACHE_PREFIX + activationCode;
        redisTemplate.opsForValue().set(key, user, CACHE_EXPIRE_TIME, TimeUnit.HOURS);
    }

    /**
     * 从缓存获取用户信息（通过激活码）
     */
    public User getUserByActivationCode(String activationCode) {
        String key = ACTIVATION_CODE_CACHE_PREFIX + activationCode;
        Object cached = redisTemplate.opsForValue().get(key);
        return cached != null ? (User) cached : null;
    }

    /**
     * 缓存用户信息（通过用户ID）
     */
    public void cacheUserById(String userId, User user) {
        String key = USER_CACHE_PREFIX + userId;
        redisTemplate.opsForValue().set(key, user, CACHE_EXPIRE_TIME, TimeUnit.HOURS);
    }

    /**
     * 从缓存获取用户信息（通过用户ID）
     */
    public User getUserById(String userId) {
        String key = USER_CACHE_PREFIX + userId;
        Object cached = redisTemplate.opsForValue().get(key);
        return cached != null ? (User) cached : null;
    }

    /**
     * 删除用户缓存
     */
    public void evictUserCache(String userId, String activationCode) {
        if (userId != null) {
            redisTemplate.delete(USER_CACHE_PREFIX + userId);
        }
        if (activationCode != null) {
            redisTemplate.delete(ACTIVATION_CODE_CACHE_PREFIX + activationCode);
        }
    }

    /**
     * 缓存激活码验证失败结果（防止重复查询不存在的激活码）
     */
    public void cacheActivationCodeNotFound(String activationCode) {
        String key = ACTIVATION_CODE_CACHE_PREFIX + activationCode + ":not_found";
        // 24小时过期
        redisTemplate.opsForValue().set(key, "NOT_FOUND", CACHE_EXPIRE_TIME, TimeUnit.HOURS);
    }

    /**
     * 检查激活码是否已被标记为不存在
     */
    public boolean isActivationCodeNotFound(String activationCode) {
        String key = ACTIVATION_CODE_CACHE_PREFIX + activationCode + ":not_found";
        return redisTemplate.hasKey(key);
    }

    /**
     * 预热缓存 - 批量加载热点用户数据
     */
    public void warmUpCache(java.util.List<User> users) {
        for (User user : users) {
            if (user.getUserId() != null) {
                cacheUserById(user.getUserId(), user);
            }
            if (user.getActivationCode() != null) {
                cacheUserByActivationCode(user.getActivationCode(), user);
            }
        }
    }

    /**
     * 清理所有用户缓存（用于解决缓存兼容性问题）
     */
    public void clearAllUserCache() {
        try {
            // 删除所有用户相关的缓存键
            java.util.Set<String> userKeys = redisTemplate.keys(USER_CACHE_PREFIX + "*");
            java.util.Set<String> activationKeys = redisTemplate.keys(ACTIVATION_CODE_CACHE_PREFIX + "*");

            if (userKeys != null && !userKeys.isEmpty()) {
                redisTemplate.delete(userKeys);
                System.out.println("清理了 " + userKeys.size() + " 个用户缓存");
            }

            if (activationKeys != null && !activationKeys.isEmpty()) {
                redisTemplate.delete(activationKeys);
                System.out.println("清理了 " + activationKeys.size() + " 个激活码缓存");
            }
        } catch (Exception e) {
            System.err.println("清理缓存时发生异常: " + e.getMessage());
        }
    }
}
