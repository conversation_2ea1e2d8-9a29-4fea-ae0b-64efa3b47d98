package com.aug.usermanager.dto;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 激活码生成请求DTO
 */
public class ActivationCodeRequest {
    
    /**
     * 过期天数
     */
    @NotNull(message = "过期天数不能为空")
    @Min(value = 1, message = "过期天数必须大于0")
    private Integer expireDays;
    
    /**
     * 代理标识（可选，默认为"main"）
     */
    private String agent = "main";
    
    /**
     * VIP产品类型（可选，默认为"premium"）
     */
    private String product = "premium";
    
    /**
     * 权限等级（可选，默认为5）
     */
    private Integer power = 5;
    
    /**
     * 总积分（可选，默认为1000）
     */
    private Integer score = 1000;
    
    /**
     * 每日积分额度（可选，默认为100.0）
     */
    private Float dayScore = 100.0f;
    
    // 默认构造函数
    public ActivationCodeRequest() {}
    
    // 构造函数
    public ActivationCodeRequest(Integer expireDays) {
        this.expireDays = expireDays;
    }
    
    // Getters and Setters
    public Integer getExpireDays() {
        return expireDays;
    }
    
    public void setExpireDays(Integer expireDays) {
        this.expireDays = expireDays;
    }
    
    public String getAgent() {
        return agent;
    }
    
    public void setAgent(String agent) {
        this.agent = agent;
    }
    
    public String getProduct() {
        return product;
    }
    
    public void setProduct(String product) {
        this.product = product;
    }
    
    public Integer getPower() {
        return power;
    }
    
    public void setPower(Integer power) {
        this.power = power;
    }
    
    public Integer getScore() {
        return score;
    }
    
    public void setScore(Integer score) {
        this.score = score;
    }
    
    public Float getDayScore() {
        return dayScore;
    }
    
    public void setDayScore(Float dayScore) {
        this.dayScore = dayScore;
    }
    
    @Override
    public String toString() {
        return "ActivationCodeRequest{" +
                "expireDays=" + expireDays +
                ", agent='" + agent + '\'' +
                ", product='" + product + '\'' +
                ", power=" + power +
                ", score=" + score +
                ", dayScore=" + dayScore +
                '}';
    }
}
