package com.aug.usermanager.dto;

import javax.validation.constraints.NotBlank;

/**
 * 登录请求DTO
 */
public class LoginRequest {
    
    /**
     * 激活码
     */
    @NotBlank(message = "激活码不能为空")
    private String card;
    
    /**
     * 代理标识
     */
    @NotBlank(message = "代理标识不能为空")
    private String agent;
    
    // 默认构造函数
    public LoginRequest() {}
    
    // 构造函数
    public LoginRequest(String card, String agent) {
        this.card = card;
        this.agent = agent;
    }
    
    // Getters and Setters
    public String getCard() {
        return card;
    }
    
    public void setCard(String card) {
        this.card = card;
    }
    
    public String getAgent() {
        return agent;
    }
    
    public void setAgent(String agent) {
        this.agent = agent;
    }
    
    @Override
    public String toString() {
        return "LoginRequest{" +
                "card='" + card + '\'' +
                ", agent='" + agent + '\'' +
                '}';
    }
}
