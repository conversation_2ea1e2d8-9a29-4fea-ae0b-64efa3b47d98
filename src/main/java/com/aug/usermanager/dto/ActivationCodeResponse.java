package com.aug.usermanager.dto;

import com.aug.usermanager.entity.User;
import com.aug.usermanager.entity.VipInfo;

/**
 * 激活码生成响应DTO
 */
public class ActivationCodeResponse {
    
    /**
     * 生成的激活码
     */
    private String activationCode;
    
    /**
     * 用户信息
     */
    private User user;
    
    /**
     * VIP信息
     */
    private VipInfo vipInfo;
    
    /**
     * 过期时间戳
     */
    private Long expireAt;
    
    /**
     * 过期天数
     */
    private Integer expireDays;
    
    /**
     * 生成时间戳
     */
    private Long createdAt;
    
    // 默认构造函数
    public ActivationCodeResponse() {
        this.createdAt = System.currentTimeMillis();
    }
    
    // 构造函数
    public ActivationCodeResponse(String activationCode, User user, VipInfo vipInfo, Integer expireDays) {
        this();
        this.activationCode = activationCode;
        this.user = user;
        this.vipInfo = vipInfo;
        this.expireDays = expireDays;
        this.expireAt = vipInfo != null ? vipInfo.getExpireAt() : null;
    }
    
    // Getters and Setters
    public String getActivationCode() {
        return activationCode;
    }
    
    public void setActivationCode(String activationCode) {
        this.activationCode = activationCode;
    }
    
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    public VipInfo getVipInfo() {
        return vipInfo;
    }
    
    public void setVipInfo(VipInfo vipInfo) {
        this.vipInfo = vipInfo;
    }
    
    public Long getExpireAt() {
        return expireAt;
    }
    
    public void setExpireAt(Long expireAt) {
        this.expireAt = expireAt;
    }
    
    public Integer getExpireDays() {
        return expireDays;
    }
    
    public void setExpireDays(Integer expireDays) {
        this.expireDays = expireDays;
    }
    
    public Long getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }
    
    @Override
    public String toString() {
        return "ActivationCodeResponse{" +
                "activationCode='" + activationCode + '\'' +
                ", user=" + user +
                ", vipInfo=" + vipInfo +
                ", expireAt=" + expireAt +
                ", expireDays=" + expireDays +
                ", createdAt=" + createdAt +
                '}';
    }
}
