package com.aug.usermanager;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * UserManager应用程序主启动类
 */
@SpringBootApplication
@EnableTransactionManagement
public class UserManagerApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(UserManagerApplication.class, args);
        System.out.println("UserManager API服务已启动！");
        System.out.println("访问地址: http://localhost:8080/api");
        System.out.println("健康检查: http://localhost:8080/api/health");
        System.out.println("H2控制台: http://localhost:8080/api/h2-console");
    }
}
