package com.aug.usermanager.mapper;

import com.aug.usermanager.entity.VipInfo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface VipInfoMapper {

    /**
     * 根据ID查询VIP信息
     * @param id VIP信息ID
     * @return VipInfo
     */
    VipInfo findById(Long id);

    /**
     * 插入一条新的VIP信息
     * @param vipInfo VIP信息对象
     * @return 影响的行数
     */
    int insert(VipInfo vipInfo);

    /**
     * 更新VIP信息
     * @param vipInfo VIP信息对象
     * @return 影响的行数
     */
    int update(VipInfo vipInfo);

    /**
     * 根据ID删除VIP信息
     * @param id VIP信息ID
     * @return 影响的行数
     */
    int delete(Long id);
}

