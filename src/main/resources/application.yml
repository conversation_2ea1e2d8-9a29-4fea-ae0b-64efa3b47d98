server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: UserManager

  # 数据库配置
  datasource:
    # MySQL数据库配置
    url: jdbc:mysql://*************:3306/model?useSSL=false&serverTimezone=UTC&characterEncoding=utf8&autoReconnect=true&failOverReadOnly=false&maxReconnects=10
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: Cll@123?

    hikari:
      max-lifetime: 600000 # 10分钟
      validation-timeout: 5000 # 5秒
      connection-timeout: 20000 # 20秒
      idle-timeout: 300000 # 5分钟
      maximum-pool-size: 100  # 增加最大连接数以支持更高并发
      minimum-idle: 20        # 增加最小空闲连接数
      connection-test-query: SELECT 1
      leak-detection-threshold: 60000  # 连接泄漏检测阈值

  # Redis 配置
  redis:
    host: *************
    port: 6379
    password: # 如果有密码请填写
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 100   # 连接池最大连接数
        max-idle: 20      # 连接池最大空闲连接数
        min-idle: 5       # 连接池最小空闲连接数
        max-wait: 5000ms  # 连接池最大阻塞等待时间

  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 600000  # 默认缓存过期时间 10分钟

  # Jackson 配置
  jackson:
    serialization:
      write-dates-as-timestamps: false
    time-zone: GMT+8

  # 数据库初始化配置
  sql:
    init:
      mode: always
      schema-locations: classpath:schema.sql
# MyBatis 配置
mybatis:
  # 指定Mapper XML文件的位置
  mapper-locations: classpath:mapper/*.xml
  configuration:
    # 开启驼峰命名自动映射，如：`last_login_at` -> `lastLoginAt`
    map-underscore-to-camel-case: true

# JWT配置
jwt:
  secret: mySecretKey1234567890123456789012345678901234567890123456789012345678901234567890  # 生产环境请使用更复杂的密钥
  expiration: 86400000  # 24小时（毫秒）
  header: X-Auth-Token

# 日志配置
logging:
  level:
    com.cet.aug.usermanager: DEBUG
    org.springframework.security: TRACE
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 应用自定义配置
app:
  name: UserManager API
  version: 1.0.0
  description: User Management API with activation code login system

  # 激活码配置
  activation:
    code-length: 16
    expiry-days: 365

  # VIP配置
  vip:
    default-score: 1000
    default-day-score: 100.0
    default-power: 5
    default-product: premium
