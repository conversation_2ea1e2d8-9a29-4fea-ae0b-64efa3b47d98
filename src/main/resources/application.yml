server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: UserManager

  # 数据库配置
  datasource:
    # MySQL数据库配置
    url: *******************************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: Cll@123?

    hikari:
      max-lifetime: 600000 # 10分钟
      validation-timeout: 5000 # 5秒
      connection-timeout: 20000 # 20秒
      idle-timeout: 300000 # 5分钟
      maximum-pool-size: 50
      minimum-idle: 10
      connection-test-query: SELECT 1

  # 数据库初始化配置
  sql:
    init:
      mode: always
      schema-locations: classpath:schema.sql
# MyBatis 配置
mybatis:
  # 指定Mapper XML文件的位置
  mapper-locations: classpath:mapper/*.xml
  configuration:
    # 开启驼峰命名自动映射，如：`last_login_at` -> `lastLoginAt`
    map-underscore-to-camel-case: true

# JWT配置
jwt:
  secret: mySecretKey1234567890123456789012345678901234567890123456789012345678901234567890  # 生产环境请使用更复杂的密钥
  expiration: 86400000  # 24小时（毫秒）
  header: X-Auth-Token

# 日志配置
logging:
  level:
    com.cet.aug.usermanager: DEBUG
    org.springframework.security: TRACE
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 应用自定义配置
app:
  name: UserManager API
  version: 1.0.0
  description: User Management API with activation code login system

  # 激活码配置
  activation:
    code-length: 16
    expiry-days: 365

  # VIP配置
  vip:
    default-score: 1000
    default-day-score: 100.0
    default-power: 5
    default-product: premium
