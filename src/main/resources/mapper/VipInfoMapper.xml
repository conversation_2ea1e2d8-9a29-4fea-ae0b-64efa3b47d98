<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aug.usermanager.mapper.VipInfoMapper">

    <select id="findById" resultType="com.aug.usermanager.entity.VipInfo">
        SELECT * FROM vip_info WHERE id = #{id}
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO vip_info (expire_at, day_score, refresh_at, power, product, score, score_used)
        VALUES (#{expireAt}, #{dayScore}, #{refreshAt}, #{power}, #{product}, #{score}, #{scoreUsed})
    </insert>

    <update id="update">
        UPDATE vip_info
        <set>
            <if test="expireAt != null">expire_at = #{expireAt},</if>
            <if test="dayScore != null">day_score = #{dayScore},</if>
            <if test="refreshAt != null">refresh_at = #{refreshAt},</if>
            <if test="power != null">power = #{power},</if>
            <if test="product != null">product = #{product},</if>
            <if test="score != null">score = #{score},</if>
            <if test="scoreUsed != null">score_used = #{scoreUsed},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete">
        DELETE FROM vip_info WHERE id = #{id}
    </delete>

</mapper>

