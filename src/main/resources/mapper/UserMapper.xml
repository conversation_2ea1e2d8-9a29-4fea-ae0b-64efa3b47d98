<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aug.usermanager.mapper.UserMapper">

    <resultMap id="UserResultMap" type="com.aug.usermanager.entity.User">
        <id property="id" column="u_id"/>
        <result property="userId" column="u_user_id"/>
        <result property="activationCode" column="u_activation_code"/>
        <result property="agent" column="u_agent"/>
        <result property="status" column="u_status"/>
        <result property="createdAt" column="u_created_at"/>
        <result property="lastLoginAt" column="u_last_login_at"/>
        <result property="passwordHash" column="u_password_hash"/>
        <result property="vipInfoId" column="u_vip_info_id"/>
        <association property="vip" javaType="com.aug.usermanager.entity.VipInfo">
            <id property="id" column="v_id"/>
            <result property="expireAt" column="v_expire_at"/>
            <result property="dayScore" column="v_day_score"/>
            <result property="refreshAt" column="v_refresh_at"/>
            <result property="power" column="v_power"/>
            <result property="product" column="v_product"/>
            <result property="score" column="v_score"/>
            <result property="scoreUsed" column="v_score_used"/>
        </association>
    </resultMap>

    <sql id="userColumns">
        u.id as u_id, u.user_id as u_user_id, u.activation_code as u_activation_code, u.agent as u_agent,
        u.status as u_status, u.created_at as u_created_at, u.last_login_at as u_last_login_at,
        u.password_hash as u_password_hash, u.vip_info_id as u_vip_info_id
    </sql>

    <sql id="vipInfoColumns">
        v.id as v_id, v.expire_at as v_expire_at, v.day_score as v_day_score, v.refresh_at as v_refresh_at,
        v.power as v_power, v.product as v_product, v.score as v_score, v.score_used as v_score_used
    </sql>

    <select id="findById" resultMap="UserResultMap">
        SELECT <include refid="userColumns"/>, <include refid="vipInfoColumns"/>
        FROM users u
        LEFT JOIN vip_info v ON u.vip_info_id = v.id
        WHERE u.id = #{id}
    </select>

    <select id="findByUserId" resultMap="UserResultMap">
        SELECT <include refid="userColumns"/>, <include refid="vipInfoColumns"/>
        FROM users u
        LEFT JOIN vip_info v ON u.vip_info_id = v.id
        WHERE u.user_id = #{userId}
    </select>

    <select id="findByActivationCode" resultMap="UserResultMap">
        SELECT <include refid="userColumns"/>, <include refid="vipInfoColumns"/>
        FROM users u
        LEFT JOIN vip_info v ON u.vip_info_id = v.id
        WHERE u.activation_code = #{activationCode}
    </select>

    <select id="findAll" resultMap="UserResultMap">
        SELECT <include refid="userColumns"/>, <include refid="vipInfoColumns"/>
        FROM users u
        LEFT JOIN vip_info v ON u.vip_info_id = v.id
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO users (user_id, activation_code, agent, status, created_at, last_login_at, password_hash, vip_info_id)
        VALUES (#{userId}, #{activationCode}, #{agent}, #{status}, #{createdAt}, #{lastLoginAt}, #{passwordHash}, #{vipInfoId})
    </insert>

    <update id="update">
        UPDATE users
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="activationCode != null">activation_code = #{activationCode},</if>
            <if test="agent != null">agent = #{agent},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="lastLoginAt != null">last_login_at = #{lastLoginAt},</if>
            <if test="passwordHash != null">password_hash = #{passwordHash},</if>
            <if test="vipInfoId != null">vip_info_id = #{vipInfoId},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete">
        DELETE FROM users WHERE id = #{id}
    </delete>

</mapper>

