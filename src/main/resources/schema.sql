-- 创建VIP信息表
CREATE TABLE IF NOT EXISTS vip_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    expire_at BIGINT NOT NULL COMMENT '过期时间戳',
    day_score FLOAT NOT NULL DEFAULT 100.0 COMMENT '每日积分额度',
    refresh_at BIGINT NOT NULL COMMENT '积分刷新时间',
    power INT NOT NULL DEFAULT 5 COMMENT '权限等级',
    product VARCHAR(50) NOT NULL DEFAULT 'premium' COMMENT '产品类型',
    score INT NOT NULL DEFAULT 1000 COMMENT '总积分',
    score_used INT NOT NULL DEFAULT 0 COMMENT '已使用积分'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP信息表';

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL UNIQUE COMMENT '用户唯一标识',
    activation_code VARCHAR(100) NOT NULL COMMENT '激活码',
    agent VARCHAR(50) NOT NULL COMMENT '代理标识',
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '用户状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    password_hash VARCHAR(255) NULL COMMENT '密码哈希',
    vip_info_id BIGINT NULL COMMENT 'VIP信息ID',
    INDEX idx_user_id (user_id),
    INDEX idx_activation_code (activation_code),
    INDEX idx_agent (agent),
    FOREIGN KEY (vip_info_id) REFERENCES vip_info(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
