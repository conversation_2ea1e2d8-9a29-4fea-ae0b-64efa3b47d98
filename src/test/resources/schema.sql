-- H2数据库兼容的建表语句

-- VIP信息表
CREATE TABLE IF NOT EXISTS vip_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    expire_at BIGINT NOT NULL,
    day_score FLOAT NOT NULL DEFAULT 100.0,
    refresh_at BIGINT NOT NULL,
    power INT NOT NULL DEFAULT 5,
    product VARCHAR(50) NOT NULL DEFAULT 'premium',
    score INT NOT NULL DEFAULT 1000,
    score_used INT NOT NULL DEFAULT 0
);

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(100) NOT NULL UNIQUE,
    activation_code VARCHAR(255) NOT NULL UNIQUE,
    agent VARCHAR(50) NOT NULL DEFAULT 'main',
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP,
    password_hash VARCHAR(255),
    vip_info_id BIGINT,
    FOREIGN KEY (vip_info_id) REFERENCES vip_info(id)
);

-- 插入测试数据
INSERT INTO vip_info (expire_at, day_score, refresh_at, power, product, score, score_used) VALUES
(9999999999999, 100.0, 1703894400000, 8, 'premium', 2000, 0),
(9999999999999, 100.0, 1703894400000, 5, 'basic', 1000, 0);

INSERT INTO users (user_id, activation_code, agent, status, created_at, last_login_at, vip_info_id) VALUES
('test_user', 'ABCDE-12345-FGHIJ-67890-KLMNO', 'main', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1),
('demo_user', 'demo987654321', 'main', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 2);
