server:
  port: 0

spring:
  application:
    name: UserManager-Test

  # 使用内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
    hikari:
      max-lifetime: 600000
      validation-timeout: 5000
      connection-timeout: 20000
      idle-timeout: 300000
      maximum-pool-size: 20
      minimum-idle: 5
      connection-test-query: SELECT 1

  # 使用内存Redis进行测试
  redis:
    host: localhost
    port: 6379
    database: 1  # 使用不同的数据库
    timeout: 1000ms
    lettuce:
      pool:
        max-active: 10
        max-idle: 5
        min-idle: 1
        max-wait: 1000ms

  # 数据库初始化
  sql:
    init:
      mode: always
      schema-locations: classpath:schema.sql

# MyBatis 配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true

# JWT配置
jwt:
  secret: testSecretKey1234567890123456789012345678901234567890123456789012345678901234567890
  expiration: 86400000
  header: X-Auth-Token

# 日志配置
logging:
  level:
    com.aug.usermanager: DEBUG
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

# 应用自定义配置
app:
  name: UserManager API Test
  version: 1.0.0
  description: User Management API Test Environment

  # 激活码配置
  activation:
    code-length: 16
    expiry-days: 365

  # VIP配置
  vip:
    default-score: 1000
    default-day-score: 100.0
    default-power: 5
    default-product: premium
