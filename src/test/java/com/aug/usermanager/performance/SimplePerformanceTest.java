package com.aug.usermanager.performance;

import org.junit.jupiter.api.Test;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 简化的性能测试 - 不依赖Spring上下文
 */
public class SimplePerformanceTest {

    /**
     * 测试并发处理能力的基准测试
     */
    @Test
    public void testConcurrentProcessing() throws Exception {
        int threadCount = 50;  // 并发线程数
        int requestsPerThread = 100;  // 每个线程的请求数
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < requestsPerThread; j++) {
                        try {
                            // 模拟登录处理逻辑
                            simulateLoginProcess(threadIndex, j);
                            successCount.incrementAndGet();
                        } catch (Exception e) {
                            failureCount.incrementAndGet();
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        int totalRequests = threadCount * requestsPerThread;
        
        System.out.println("=== 并发处理性能测试结果 ===");
        System.out.println("总请求数: " + totalRequests);
        System.out.println("成功请求数: " + successCount.get());
        System.out.println("失败请求数: " + failureCount.get());
        System.out.println("总耗时: " + totalTime + "ms");
        System.out.println("平均响应时间: " + (totalTime / (double) totalRequests) + "ms");
        System.out.println("QPS: " + (totalRequests * 1000.0 / totalTime));
        System.out.println("成功率: " + (successCount.get() * 100.0 / totalRequests) + "%");
        
        // 验证基本性能指标
        assert successCount.get() > 0 : "应该有成功的请求";
        assert (totalRequests * 1000.0 / totalTime) > 1000 : "QPS应该大于1000";
    }

    /**
     * 测试缓存性能提升
     */
    @Test
    public void testCachePerformance() throws Exception {
        int iterations = 10000;
        
        // 测试无缓存情况
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            simulateDbQuery("test_code_" + (i % 100));
        }
        long noCacheTime = System.currentTimeMillis() - startTime;
        
        // 模拟缓存
        java.util.Map<String, String> cache = new java.util.concurrent.ConcurrentHashMap<>();
        
        // 测试有缓存情况
        startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            String key = "test_code_" + (i % 100);
            String result = cache.get(key);
            if (result == null) {
                result = simulateDbQuery(key);
                cache.put(key, result);
            }
        }
        long withCacheTime = System.currentTimeMillis() - startTime;
        
        System.out.println("=== 缓存性能测试结果 ===");
        System.out.println("无缓存耗时: " + noCacheTime + "ms");
        System.out.println("有缓存耗时: " + withCacheTime + "ms");
        System.out.println("性能提升: " + (noCacheTime / (double) withCacheTime) + "倍");
        System.out.println("缓存命中率: " + ((iterations - 100) * 100.0 / iterations) + "%");
        
        // 验证缓存确实提升了性能
        assert withCacheTime < noCacheTime : "缓存应该提升性能";
        assert (noCacheTime / (double) withCacheTime) > 2 : "性能提升应该超过2倍";
    }

    /**
     * 测试异步处理性能
     */
    @Test
    public void testAsyncPerformance() throws Exception {
        int taskCount = 1000;
        
        // 测试同步处理
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < taskCount; i++) {
            simulateSlowOperation();
        }
        long syncTime = System.currentTimeMillis() - startTime;
        
        // 测试异步处理
        ExecutorService executor = Executors.newFixedThreadPool(10);
        CountDownLatch latch = new CountDownLatch(taskCount);
        
        startTime = System.currentTimeMillis();
        for (int i = 0; i < taskCount; i++) {
            executor.submit(() -> {
                try {
                    simulateSlowOperation();
                } finally {
                    latch.countDown();
                }
            });
        }
        latch.await();
        executor.shutdown();
        long asyncTime = System.currentTimeMillis() - startTime;
        
        System.out.println("=== 异步处理性能测试结果 ===");
        System.out.println("同步处理耗时: " + syncTime + "ms");
        System.out.println("异步处理耗时: " + asyncTime + "ms");
        System.out.println("性能提升: " + (syncTime / (double) asyncTime) + "倍");
        
        // 验证异步处理确实提升了性能
        assert asyncTime < syncTime : "异步处理应该更快";
        assert (syncTime / (double) asyncTime) > 5 : "异步处理性能提升应该超过5倍";
    }

    /**
     * 模拟登录处理逻辑
     */
    private void simulateLoginProcess(int threadIndex, int requestIndex) throws Exception {
        // 模拟参数验证
        Thread.sleep(1);
        
        // 模拟缓存查询
        if (Math.random() > 0.8) { // 20%缓存未命中
            Thread.sleep(5); // 模拟数据库查询
        }
        
        // 模拟业务逻辑处理
        Thread.sleep(2);
        
        // 模拟JWT生成
        Thread.sleep(1);
    }

    /**
     * 模拟数据库查询
     */
    private String simulateDbQuery(String key) {
        try {
            Thread.sleep(10); // 模拟数据库查询延迟
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return "result_for_" + key;
    }

    /**
     * 模拟慢操作
     */
    private void simulateSlowOperation() {
        try {
            Thread.sleep(50); // 模拟耗时操作
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
