package com.aug.usermanager.performance;

import com.aug.usermanager.dto.LoginRequest;
import com.aug.usermanager.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 并发登录测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class ConcurrentLoginTest {

    @Autowired
    private UserService userService;

    /**
     * 测试并发登录性能 - 直接调用服务层
     */
    @Test
    public void testConcurrentLogin() throws Exception {
        int threadCount = 50;  // 并发线程数
        int requestsPerThread = 10;  // 每个线程的请求数

        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);

        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);

        long startTime = System.currentTimeMillis();

        // 创建测试用的登录请求
        LoginRequest loginRequest = new LoginRequest("ABCDE-12345-FGHIJ-67890-KLMNO", "main");

        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < requestsPerThread; j++) {
                        try {
                            // 直接调用服务层方法进行测试
                            userService.cardLogin(loginRequest);
                            successCount.incrementAndGet();
                        } catch (Exception e) {
                            failureCount.incrementAndGet();
                            // 预期会有一些失败（激活码不存在等）
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        int totalRequests = threadCount * requestsPerThread;
        
        System.out.println("=== 并发登录测试结果 ===");
        System.out.println("总请求数: " + totalRequests);
        System.out.println("成功请求数: " + successCount.get());
        System.out.println("失败请求数: " + failureCount.get());
        System.out.println("总耗时: " + totalTime + "ms");
        System.out.println("平均响应时间: " + (totalTime / (double) totalRequests) + "ms");
        System.out.println("QPS: " + (totalRequests * 1000.0 / totalTime));
        System.out.println("成功率: " + (successCount.get() * 100.0 / totalRequests) + "%");
    }

    /**
     * 测试不同激活码的并发登录
     */
    @Test
    public void testConcurrentLoginWithDifferentCodes() throws Exception {
        int threadCount = 20;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);

        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    // 使用不同的激活码进行测试
                    String activationCode = "TEST-" + String.format("%05d", threadIndex) + "-CODE-12345-ABCDE";
                    LoginRequest loginRequest = new LoginRequest(activationCode, "main");

                    for (int j = 0; j < 5; j++) {
                        try {
                            userService.cardLogin(loginRequest);
                            successCount.incrementAndGet();
                        } catch (Exception e) {
                            failureCount.incrementAndGet();
                            // 预期大部分会失败（激活码不存在）
                        }
                    }
                } catch (Exception e) {
                    System.err.println("线程 " + threadIndex + " 执行失败: " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        int totalRequests = threadCount * 5;
        
        System.out.println("=== 不同激活码并发测试结果 ===");
        System.out.println("总请求数: " + totalRequests);
        System.out.println("处理请求数: " + successCount.get());
        System.out.println("异常请求数: " + failureCount.get());
        System.out.println("总耗时: " + totalTime + "ms");
        System.out.println("平均响应时间: " + (totalTime / (double) totalRequests) + "ms");
        System.out.println("QPS: " + (totalRequests * 1000.0 / totalTime));
    }
}
