package com.aug.usermanager.controller;

import com.aug.usermanager.dto.ActivationCodeRequest;
import com.aug.usermanager.dto.ActivationCodeResponse;
import com.aug.usermanager.dto.LoginRequest;
import com.aug.usermanager.entity.User;
import com.aug.usermanager.entity.VipInfo;
import com.aug.usermanager.service.UserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureWebMvc
public class UserControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @MockBean
    private UserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    @org.junit.jupiter.api.BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }
    
    @Test
    public void testCardLogin_Success() throws Exception {
        // 准备测试数据
        LoginRequest loginRequest = new LoginRequest("test123456789", "main");
        User mockUser = new User();
        mockUser.setUserId("test_user");
        mockUser.setToken("mock_token");
        
        // 模拟服务层行为
        when(userService.cardLogin(any(LoginRequest.class))).thenReturn(mockUser);
        
        // 执行测试
        mockMvc.perform(post("/users/card-login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("success"))
                .andExpect(jsonPath("$.data.id").value("test_user"));
    }
    
    @Test
    public void testCardLogin_InvalidRequest() throws Exception {
        // 准备无效的测试数据
        LoginRequest loginRequest = new LoginRequest("", "main");

        // 执行测试
        mockMvc.perform(post("/users/card-login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400));
    }

    @Test
    public void testCardLogin_NonExistentCode() throws Exception {
        // 准备测试数据
        LoginRequest loginRequest = new LoginRequest("NONEX-12345-ISTENT-67890-CODES", "main");

        // 模拟服务层抛出异常
        when(userService.cardLogin(any(LoginRequest.class)))
                .thenThrow(new IllegalArgumentException("激活码不存在，登录失败"));

        // 执行测试
        mockMvc.perform(post("/users/card-login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("激活码不存在，登录失败"));
    }

    @Test
    public void testCardLogin_ExpiredCode() throws Exception {
        // 准备测试数据
        LoginRequest loginRequest = new LoginRequest("EXPIR-12345-EDCOD-67890-ETEST", "main");

        // 模拟服务层抛出异常
        when(userService.cardLogin(any(LoginRequest.class)))
                .thenThrow(new IllegalArgumentException("激活码已过期，过期时间：Mon Jan 01 00:00:00 CST 2024，登录失败"));

        // 执行测试
        mockMvc.perform(post("/users/card-login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value(org.hamcrest.Matchers.containsString("激活码已过期")));
    }

    @Test
    public void testGenerateActivationCode_Success() throws Exception {
        // 准备测试数据
        ActivationCodeRequest request = new ActivationCodeRequest();
        request.setExpireDays(365);
        request.setAgent("main");
        request.setProduct("premium");
        request.setPower(5);
        request.setScore(1000);
        request.setDayScore(100.0f);

        // 准备模拟响应
        User mockUser = new User();
        mockUser.setUserId("test_user");
        mockUser.setActivationCode("ABCDE-12345-FGHIJ-67890-KLMNO");

        VipInfo mockVipInfo = new VipInfo();
        mockVipInfo.setExpireAt(System.currentTimeMillis() + 365L * 24 * 60 * 60 * 1000);
        mockVipInfo.setProduct("premium");
        mockVipInfo.setPower(5);

        ActivationCodeResponse mockResponse = new ActivationCodeResponse(
                "ABCDE-12345-FGHIJ-67890-KLMNO", mockUser, mockVipInfo, 365);

        // 模拟服务层行为
        when(userService.generateActivationCode(any(ActivationCodeRequest.class)))
                .thenReturn(mockResponse);

        // 执行测试
        mockMvc.perform(post("/users/generate-activation-code")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("success"))
                .andExpect(jsonPath("$.data.activationCode").value("ABCDE-12345-FGHIJ-67890-KLMNO"));
    }

    @Test
    public void testGenerateActivationCode_InvalidRequest() throws Exception {
        // 准备无效的测试数据（过期天数为0）
        ActivationCodeRequest request = new ActivationCodeRequest();
        request.setExpireDays(0);

        // 执行测试
        mockMvc.perform(post("/users/generate-activation-code")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400));
    }
}
