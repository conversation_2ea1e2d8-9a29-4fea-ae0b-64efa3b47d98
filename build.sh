#!/bin/bash

# UserManager 项目构建脚本
# 用于构建前端和后端项目

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装 $1"
        exit 1
    fi
}

# 检查必要的工具
check_prerequisites() {
    log_info "检查构建环境..."
    
    check_command "java"
    check_command "mvn"
    check_command "node"
    check_command "npm"
    check_command "docker"
    
    log_success "构建环境检查完成"
}

# 清理旧的构建产物
clean_build() {
    log_info "清理旧的构建产物..."
    
    # 清理后端构建产物
    if [ -d "target" ]; then
        rm -rf target
        log_info "已清理后端构建目录"
    fi
    
    # 清理前端构建产物
    if [ -d "user-manager-frontend/dist" ]; then
        rm -rf user-manager-frontend/dist
        log_info "已清理前端构建目录"
    fi
    
    # 清理前端node_modules（可选）
    if [ "$1" = "--clean-all" ]; then
        if [ -d "user-manager-frontend/node_modules" ]; then
            rm -rf user-manager-frontend/node_modules
            log_info "已清理前端依赖目录"
        fi
    fi
    
    log_success "构建产物清理完成"
}

# 构建后端项目
build_backend() {
    log_info "开始构建后端项目..."
    
    # 检查pom.xml是否存在
    if [ ! -f "pom.xml" ]; then
        log_error "pom.xml 文件未找到"
        exit 1
    fi
    
    # Maven构建
    log_info "执行Maven构建..."
    mvn clean package -DskipTests
    
    # 检查构建结果
    if [ -f "target/UserManager-1.0-SNAPSHOT.jar" ]; then
        log_success "后端项目构建成功"
        log_info "JAR文件位置: target/UserManager-1.0-SNAPSHOT.jar"
    else
        log_error "后端项目构建失败"
        exit 1
    fi
}

# 构建前端项目
build_frontend() {
    log_info "开始构建前端项目..."
    
    # 进入前端目录
    cd user-manager-frontend
    
    # 检查package.json是否存在
    if [ ! -f "package.json" ]; then
        log_error "package.json 文件未找到"
        exit 1
    fi
    
    # 安装依赖
    log_info "安装前端依赖..."
    npm install
    
    # 构建项目
    log_info "执行前端构建..."
    npm run build
    
    # 检查构建结果
    if [ -d "dist" ]; then
        log_success "前端项目构建成功"
        log_info "构建产物位置: user-manager-frontend/dist"
    else
        log_error "前端项目构建失败"
        exit 1
    fi
    
    # 返回根目录
    cd ..
}

# 构建Docker镜像
build_docker_images() {
    log_info "开始构建Docker镜像..."
    
    # 构建后端镜像
    log_info "构建后端Docker镜像..."
    docker build -f Dockerfile.backend -t usermanager-backend:latest .
    
    # 构建前端镜像
    log_info "构建前端Docker镜像..."
    docker build -f Dockerfile.frontend -t usermanager-frontend:latest .
    
    log_success "Docker镜像构建完成"
    
    # 显示镜像信息
    log_info "Docker镜像列表:"
    docker images | grep usermanager
}

# 显示帮助信息
show_help() {
    echo "UserManager 项目构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --backend-only    仅构建后端项目"
    echo "  --frontend-only   仅构建前端项目"
    echo "  --docker-only     仅构建Docker镜像（需要先构建项目）"
    echo "  --clean-all       清理所有构建产物和依赖"
    echo "  --skip-docker     跳过Docker镜像构建"
    echo "  --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                构建所有项目和Docker镜像"
    echo "  $0 --backend-only 仅构建后端项目"
    echo "  $0 --clean-all    清理所有构建产物后重新构建"
}

# 主函数
main() {
    log_info "UserManager 项目构建开始..."
    
    # 解析命令行参数
    BACKEND_ONLY=false
    FRONTEND_ONLY=false
    DOCKER_ONLY=false
    SKIP_DOCKER=false
    CLEAN_ALL=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --backend-only)
                BACKEND_ONLY=true
                shift
                ;;
            --frontend-only)
                FRONTEND_ONLY=true
                shift
                ;;
            --docker-only)
                DOCKER_ONLY=true
                shift
                ;;
            --skip-docker)
                SKIP_DOCKER=true
                shift
                ;;
            --clean-all)
                CLEAN_ALL=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查环境
    check_prerequisites
    
    # 清理构建产物
    if [ "$CLEAN_ALL" = true ]; then
        clean_build --clean-all
    else
        clean_build
    fi
    
    # 根据参数执行相应的构建
    if [ "$DOCKER_ONLY" = true ]; then
        build_docker_images
    elif [ "$BACKEND_ONLY" = true ]; then
        build_backend
    elif [ "$FRONTEND_ONLY" = true ]; then
        build_frontend
    else
        # 构建所有项目
        build_backend
        build_frontend
        
        if [ "$SKIP_DOCKER" != true ]; then
            build_docker_images
        fi
    fi
    
    log_success "UserManager 项目构建完成！"
    
    # 显示构建结果摘要
    echo ""
    log_info "构建结果摘要:"
    if [ "$FRONTEND_ONLY" != true ] && [ "$DOCKER_ONLY" != true ]; then
        echo "  - 后端JAR: target/UserManager-1.0-SNAPSHOT.jar"
    fi
    if [ "$BACKEND_ONLY" != true ] && [ "$DOCKER_ONLY" != true ]; then
        echo "  - 前端构建: user-manager-frontend/dist/"
    fi
    if [ "$SKIP_DOCKER" != true ] && [ "$BACKEND_ONLY" != true ] && [ "$FRONTEND_ONLY" != true ]; then
        echo "  - Docker镜像: usermanager-backend:latest, usermanager-frontend:latest"
    fi
}

# 执行主函数
main "$@"
