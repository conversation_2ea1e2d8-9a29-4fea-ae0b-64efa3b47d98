# UserManager 一键部署方案

## 🚀 快速开始

UserManager 是一个现代化的用户管理系统，采用 Spring Boot + Vue.js 架构，支持激活码登录和VIP管理功能。

### 一键部署命令

```bash
# 克隆项目
git clone <your-repository>
cd UserManager

# 快速部署
chmod +x quick-start.sh
./quick-start.sh
```

## 📋 部署文件说明

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `quick-start.sh` | 快速部署脚本 | 一键完成所有部署步骤 |
| `build.sh` | 项目构建脚本 | 构建前端和后端项目 |
| `deploy.sh` | 服务部署脚本 | 管理Docker服务的启停 |
| `health-check.sh` | 健康检查脚本 | 监控系统运行状态 |
| `docker-compose.yml` | Docker编排文件 | 定义所有服务配置 |
| `Dockerfile.backend` | 后端镜像构建 | Spring Boot应用容器化 |
| `Dockerfile.frontend` | 前端镜像构建 | Vue.js应用容器化 |
| `nginx.conf` | Nginx配置 | 反向代理和静态文件服务 |
| `application-prod.yml` | 生产环境配置 | Spring Boot生产配置 |
| `init.sql` | 数据库初始化 | 创建表结构和初始数据 |
| `.env.example` | 环境变量模板 | 配置参数模板 |

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx         │    │   Spring Boot   │    │   MySQL         │
│   (前端 + 代理)  │────│   (后端API)     │────│   (数据库)      │
│   Port: 80      │    │   Port: 8080    │    │   Port: 3306    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📦 部署组件

### 前端服务 (Nginx + Vue.js)
- **技术栈**: Vue 3 + Element Plus + Nginx
- **端口**: 80
- **功能**: 用户界面、静态文件服务、API代理

### 后端服务 (Spring Boot)
- **技术栈**: Spring Boot 2.7 + MyBatis + Spring Security
- **端口**: 8080
- **功能**: REST API、业务逻辑、JWT认证

### 数据库服务 (MySQL)
- **版本**: MySQL 8.0
- **端口**: 3306
- **功能**: 数据存储、用户管理、VIP信息

## 🛠️ 部署步骤

### 1. 环境准备
```bash
# 安装Docker和Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 项目部署
```bash
# 方式一：快速部署（推荐）
./quick-start.sh

# 方式二：分步部署
./build.sh          # 构建项目
./deploy.sh         # 部署服务
```

### 3. 验证部署
```bash
# 健康检查
./health-check.sh

# 访问应用
curl http://localhost/health      # 前端健康检查
curl http://localhost:8080/api/health  # 后端健康检查
```

## 🔧 管理命令

### 服务管理
```bash
./deploy.sh --status    # 查看服务状态
./deploy.sh --logs      # 查看服务日志
./deploy.sh --restart   # 重启所有服务
./deploy.sh --stop      # 停止所有服务
```

### 构建管理
```bash
./build.sh --backend-only   # 仅构建后端
./build.sh --frontend-only  # 仅构建前端
./build.sh --clean-all      # 清理后重新构建
```

### 健康监控
```bash
./health-check.sh           # 全面健康检查
./health-check.sh --report  # 生成详细报告
./health-check.sh --backend # 仅检查后端
```

## 🌐 访问地址

部署完成后，可通过以下地址访问：

- **前端应用**: http://your-server-ip
- **后端API**: http://your-server-ip:8080/api
- **健康检查**: http://your-server-ip/health
- **API文档**: http://your-server-ip:8080/api/swagger-ui.html

## 👤 默认账户

- **用户名**: admin
- **密码**: admin123

> ⚠️ **安全提醒**: 生产环境请立即修改默认密码！

## 🔒 安全配置

### 生产环境必做事项

1. **修改默认密码**
```bash
# 编辑环境配置
vim .env

# 修改以下配置
MYSQL_ROOT_PASSWORD=your_secure_password
MYSQL_PASSWORD=your_secure_password
JWT_SECRET=your_256_bit_secret_key
```

2. **启用HTTPS**
```bash
# 使用nginx-prod.conf配置SSL
cp nginx-prod.conf nginx.conf
# 配置SSL证书路径
```

3. **防火墙配置**
```bash
# 只开放必要端口
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

## 📊 监控和维护

### 日志查看
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f frontend
docker-compose logs -f backend
docker-compose logs -f mysql
```

### 数据备份
```bash
# 备份数据库
docker-compose exec mysql mysqldump -u root -p model > backup_$(date +%Y%m%d).sql

# 恢复数据库
docker-compose exec -T mysql mysql -u root -p model < backup_file.sql
```

### 性能监控
```bash
# 查看容器资源使用
docker stats

# 查看系统资源
./health-check.sh --resources
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查看端口占用
netstat -tlnp | grep :80
# 停止占用进程
sudo kill -9 <PID>
```

2. **容器启动失败**
```bash
# 查看容器日志
docker-compose logs [service_name]
# 重新构建
docker-compose up --build -d
```

3. **数据库连接失败**
```bash
# 检查数据库状态
docker-compose exec mysql mysql -u root -p -e "SELECT 1"
# 重置数据库
docker-compose down -v && docker-compose up -d
```

### 获取帮助
```bash
./quick-start.sh --help
./build.sh --help
./deploy.sh --help
./health-check.sh --help
```

## 📈 升级指南

### 应用升级
```bash
# 备份数据
./health-check.sh --report

# 停止服务
./deploy.sh --stop

# 更新代码
git pull origin main

# 重新部署
./build.sh
./deploy.sh
```

### 回滚操作
```bash
# 回滚到指定版本
git checkout <commit_hash>
./build.sh
./deploy.sh --restart
```

## 📝 配置说明

### 环境变量
主要配置项在 `.env` 文件中：
- `MYSQL_*`: 数据库配置
- `JWT_SECRET`: JWT密钥
- `*_PORT`: 服务端口配置

### 应用配置
- `application-prod.yml`: Spring Boot生产配置
- `nginx.conf`: Nginx反向代理配置
- `docker-compose.yml`: Docker服务编排

## 🤝 技术支持

如遇到问题，请：
1. 查看日志文件
2. 运行健康检查
3. 参考故障排除指南
4. 联系技术支持

---

**注意**: 本部署方案适用于生产环境，已包含安全配置和性能优化。部署前请仔细阅读文档并根据实际环境调整配置。
