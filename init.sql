-- UserManager 数据库初始化脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS model CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE model;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密后）',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(50) COMMENT '真实姓名',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    role VARCHAR(20) DEFAULT 'USER' COMMENT '角色：USER-普通用户，ADMIN-管理员',
    last_login_at DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建激活码表
CREATE TABLE IF NOT EXISTS activation_codes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(32) NOT NULL UNIQUE COMMENT '激活码',
    user_id BIGINT COMMENT '关联用户ID',
    status TINYINT DEFAULT 0 COMMENT '状态：0-未使用，1-已使用，2-已过期',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    used_at DATETIME COMMENT '使用时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_code (code),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='激活码表';

-- 创建VIP信息表
CREATE TABLE IF NOT EXISTS vip_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    score INT DEFAULT 0 COMMENT '积分',
    day_score DECIMAL(10,2) DEFAULT 0.00 COMMENT '日积分',
    power INT DEFAULT 0 COMMENT '权力值',
    product VARCHAR(50) COMMENT '产品类型',
    vip_level TINYINT DEFAULT 0 COMMENT 'VIP等级：0-普通，1-VIP1，2-VIP2，3-VIP3',
    expires_at DATETIME COMMENT 'VIP过期时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_vip_level (vip_level),
    INDEX idx_expires_at (expires_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='VIP信息表';

-- 创建系统日志表
CREATE TABLE IF NOT EXISTS system_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT COMMENT '用户ID',
    action VARCHAR(50) NOT NULL COMMENT '操作类型',
    description TEXT COMMENT '操作描述',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- 插入默认管理员用户（密码：admin123）
INSERT IGNORE INTO users (username, password, email, real_name, role, status) VALUES 
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', '<EMAIL>', '系统管理员', 'ADMIN', 1);

-- 为管理员创建VIP信息
INSERT IGNORE INTO vip_info (user_id, score, day_score, power, product, vip_level) 
SELECT id, 10000, 1000.00, 10, 'premium', 3 FROM users WHERE username = 'admin';

-- 创建一些测试激活码
INSERT IGNORE INTO activation_codes (code, status, expires_at) VALUES 
('TEST-CODE-001', 0, DATE_ADD(NOW(), INTERVAL 1 YEAR)),
('TEST-CODE-002', 0, DATE_ADD(NOW(), INTERVAL 1 YEAR)),
('TEST-CODE-003', 0, DATE_ADD(NOW(), INTERVAL 1 YEAR));

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_users_status_role ON users(status, role);
CREATE INDEX IF NOT EXISTS idx_activation_codes_status_expires ON activation_codes(status, expires_at);
CREATE INDEX IF NOT EXISTS idx_vip_info_level_expires ON vip_info(vip_level, expires_at);

-- 创建视图：用户详细信息
CREATE OR REPLACE VIEW user_details AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.phone,
    u.real_name,
    u.avatar_url,
    u.status,
    u.role,
    u.last_login_at,
    u.last_login_ip,
    u.created_at,
    u.updated_at,
    v.score,
    v.day_score,
    v.power,
    v.product,
    v.vip_level,
    v.expires_at as vip_expires_at
FROM users u
LEFT JOIN vip_info v ON u.id = v.user_id;

-- 设置数据库字符集
ALTER DATABASE model CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
