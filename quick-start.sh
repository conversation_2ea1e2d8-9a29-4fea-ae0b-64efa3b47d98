#!/bin/bash

# UserManager 快速启动脚本
# 用于首次部署或快速重新部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo "=================================================="
    echo "       UserManager 快速部署脚本"
    echo "=================================================="
    echo ""
    echo "此脚本将帮助您快速部署 UserManager 应用"
    echo "包括前端 Vue.js 应用和后端 Spring Boot API"
    echo ""
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        log_warning "建议在 Linux 系统上运行此脚本"
    fi
    
    # 检查必要命令
    local required_commands=("docker" "docker-compose" "curl" "git")
    local missing_commands=()
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v $cmd &> /dev/null; then
            missing_commands+=($cmd)
        fi
    done
    
    if [ ${#missing_commands[@]} -ne 0 ]; then
        log_error "缺少必要的命令: ${missing_commands[*]}"
        log_info "请先安装缺少的命令，然后重新运行此脚本"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用情况..."
    
    local ports=(80 8080 3306)
    local occupied_ports=()
    
    for port in "${ports[@]}"; do
        if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
            occupied_ports+=($port)
        fi
    done
    
    if [ ${#occupied_ports[@]} -ne 0 ]; then
        log_warning "以下端口被占用: ${occupied_ports[*]}"
        echo "是否继续部署？占用的端口可能导致服务启动失败。(y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_info "部署已取消"
            exit 0
        fi
    else
        log_success "端口检查通过"
    fi
}

# 创建环境配置
setup_environment() {
    log_info "设置环境配置..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            log_success "已创建环境配置文件 .env"
        else
            log_warning ".env.example 文件不存在，创建默认配置"
            cat > .env << EOF
MYSQL_ROOT_PASSWORD=Cll@123?
MYSQL_DATABASE=model
MYSQL_USER=usermanager
MYSQL_PASSWORD=usermanager123
MYSQL_PORT=3306
SPRING_PROFILES_ACTIVE=prod
JWT_SECRET=mySecretKey1234567890123456789012345678901234567890123456789012345678901234567890
FRONTEND_PORT=80
BACKEND_PORT=8080
EOF
        fi
        
        log_warning "请检查并修改 .env 文件中的配置，特别是密码和密钥"
        echo "是否现在编辑配置文件？(y/N)"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            ${EDITOR:-nano} .env
        fi
    else
        log_info "环境配置文件已存在"
    fi
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    if [ -f "build.sh" ]; then
        chmod +x build.sh
        ./build.sh
    else
        log_error "构建脚本 build.sh 不存在"
        exit 1
    fi
}

# 部署服务
deploy_services() {
    log_info "部署服务..."
    
    if [ -f "deploy.sh" ]; then
        chmod +x deploy.sh
        ./deploy.sh
    else
        log_error "部署脚本 deploy.sh 不存在"
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署结果..."
    
    local max_attempts=30
    local attempt=1
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查前端
    log_info "检查前端服务..."
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost/health &> /dev/null; then
            log_success "前端服务正常"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_warning "前端服务检查超时"
        fi
        
        sleep 2
        ((attempt++))
    done
    
    # 检查后端
    log_info "检查后端服务..."
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8080/api/health &> /dev/null; then
            log_success "后端服务正常"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_warning "后端服务检查超时"
        fi
        
        sleep 2
        ((attempt++))
    done
}

# 显示部署结果
show_results() {
    echo ""
    echo "=================================================="
    echo "           部署完成"
    echo "=================================================="
    echo ""
    echo "服务访问地址："
    echo "  前端应用: http://localhost"
    echo "  后端API:  http://localhost:8080/api"
    echo "  健康检查: http://localhost/health"
    echo ""
    echo "管理命令："
    echo "  查看状态: ./deploy.sh --status"
    echo "  查看日志: ./deploy.sh --logs"
    echo "  停止服务: ./deploy.sh --stop"
    echo "  重启服务: ./deploy.sh --restart"
    echo ""
    echo "默认管理员账户："
    echo "  用户名: admin"
    echo "  密码: admin123"
    echo ""
    log_warning "生产环境请务必修改默认密码！"
    echo ""
}

# 主函数
main() {
    show_welcome
    
    # 检查参数
    if [[ "$1" == "--help" || "$1" == "-h" ]]; then
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  --skip-checks    跳过系统检查"
        echo "  --help, -h       显示帮助信息"
        echo ""
        exit 0
    fi
    
    # 执行部署步骤
    if [[ "$1" != "--skip-checks" ]]; then
        check_requirements
        check_ports
    fi
    
    setup_environment
    build_project
    deploy_services
    verify_deployment
    show_results
    
    log_success "UserManager 快速部署完成！"
}

# 执行主函数
main "$@"
