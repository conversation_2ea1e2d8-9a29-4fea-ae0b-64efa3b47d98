# UserManager 一键部署指南

## 概述

UserManager 是一个基于 Spring Boot + Vue.js 的用户管理系统，支持激活码登录和VIP管理功能。本文档提供了完整的一键部署方案，使用 Docker 和 Docker Compose 进行容器化部署。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx         │    │   Spring Boot   │    │   MySQL         │
│   (前端 + 代理)  │────│   (后端API)     │────│   (数据库)      │
│   Port: 80      │    │   Port: 8080    │    │   Port: 3306    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 环境要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **磁盘**: 20GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+ 或 CentOS 7+)
- **Docker**: 20.10.0+
- **Docker Compose**: 1.29.0+
- **Git**: 2.0+

### 端口要求
确保以下端口未被占用：
- `80`: 前端服务
- `8080`: 后端API服务
- `3306`: MySQL数据库

## 快速部署

### 1. 环境准备

#### 安装Docker和Docker Compose

**Ubuntu/Debian:**
```bash
# 更新包索引
sudo apt update

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 将当前用户添加到docker组
sudo usermod -aG docker $USER
```

**CentOS/RHEL:**
```bash
# 安装Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker
```

### 2. 获取项目代码

```bash
# 克隆项目（如果使用Git）
git clone <your-repository-url>
cd UserManager

# 或者上传项目文件到服务器
```

### 3. 配置环境变量

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑环境配置
vim .env
```

环境变量说明：
```bash
# MySQL配置
MYSQL_ROOT_PASSWORD=your_secure_password
MYSQL_DATABASE=model
MYSQL_USER=usermanager
MYSQL_PASSWORD=your_secure_password

# 应用配置
SPRING_PROFILES_ACTIVE=prod
JWT_SECRET=your_jwt_secret_key_at_least_256_bits

# 端口配置
FRONTEND_PORT=80
BACKEND_PORT=8080
MYSQL_PORT=3306
```

### 4. 一键部署

```bash
# 给脚本执行权限
chmod +x build.sh deploy.sh

# 构建项目
./build.sh

# 部署服务
./deploy.sh
```

### 5. 验证部署

部署完成后，访问以下地址验证：

- **前端应用**: http://your-server-ip
- **后端API**: http://your-server-ip:8080/api/health
- **API文档**: http://your-server-ip:8080/api/swagger-ui.html

## 详细部署步骤

### 步骤1: 项目构建

```bash
# 构建所有组件
./build.sh

# 或者分别构建
./build.sh --backend-only   # 仅构建后端
./build.sh --frontend-only  # 仅构建前端
./build.sh --docker-only    # 仅构建Docker镜像
```

### 步骤2: 服务部署

```bash
# 完整部署
./deploy.sh

# 其他操作
./deploy.sh --stop      # 停止服务
./deploy.sh --restart   # 重启服务
./deploy.sh --status    # 查看状态
./deploy.sh --logs      # 查看日志
./deploy.sh --clean     # 清理资源
```

### 步骤3: 服务管理

```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f [service_name]

# 重启特定服务
docker-compose restart [service_name]

# 进入容器
docker-compose exec [service_name] bash
```

## 配置说明

### 数据库配置

默认使用MySQL 8.0，配置文件：`application-prod.yml`

```yaml
spring:
  datasource:
    url: *****************************
    username: usermanager
    password: usermanager123
```

### 前端配置

Vue.js应用配置文件：`user-manager-frontend/vue.config.js`

```javascript
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://backend:8080',
        changeOrigin: true
      }
    }
  }
}
```

### Nginx配置

反向代理配置：`nginx.conf`
- 前端静态文件服务
- API请求代理到后端
- 静态资源缓存
- Gzip压缩

## 监控和维护

### 健康检查

```bash
# 检查所有服务健康状态
./deploy.sh --status

# 手动健康检查
curl http://localhost/health
curl http://localhost:8080/api/health
```

### 日志管理

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f frontend
docker-compose logs -f backend
docker-compose logs -f mysql

# 日志文件位置
# 后端日志: ./logs/usermanager.log
# Nginx日志: /var/log/nginx/
```

### 数据备份

```bash
# 备份MySQL数据
docker-compose exec mysql mysqldump -u root -p model > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据
docker-compose exec -T mysql mysql -u root -p model < backup_file.sql
```

## 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
netstat -tlnp | grep :80
netstat -tlnp | grep :8080
netstat -tlnp | grep :3306

# 停止占用端口的进程
sudo kill -9 <PID>
```

#### 2. Docker服务启动失败
```bash
# 检查Docker服务状态
sudo systemctl status docker

# 重启Docker服务
sudo systemctl restart docker

# 查看Docker日志
sudo journalctl -u docker.service
```

#### 3. 容器启动失败
```bash
# 查看容器日志
docker-compose logs [service_name]

# 检查容器状态
docker-compose ps

# 重新构建容器
docker-compose up --build -d
```

#### 4. 数据库连接失败
```bash
# 检查MySQL容器状态
docker-compose exec mysql mysql -u root -p -e "SELECT 1"

# 检查网络连接
docker-compose exec backend ping mysql

# 重置数据库
docker-compose down -v
docker-compose up -d
```

#### 5. 前端无法访问后端API
```bash
# 检查Nginx配置
docker-compose exec frontend nginx -t

# 重新加载Nginx配置
docker-compose exec frontend nginx -s reload

# 检查后端服务
curl http://localhost:8080/api/health
```

### 性能优化

#### 1. 数据库优化
- 调整MySQL配置参数
- 添加适当的索引
- 定期清理日志

#### 2. 应用优化
- 调整JVM参数
- 配置连接池
- 启用缓存

#### 3. Nginx优化
- 启用Gzip压缩
- 配置静态资源缓存
- 调整worker进程数

## 安全建议

1. **修改默认密码**: 更改数据库和应用的默认密码
2. **启用HTTPS**: 配置SSL证书
3. **防火墙配置**: 只开放必要的端口
4. **定期更新**: 保持系统和依赖的最新版本
5. **备份策略**: 建立定期备份机制

## 升级指南

### 应用升级
```bash
# 停止服务
./deploy.sh --stop

# 备份数据
docker-compose exec mysql mysqldump -u root -p model > backup_before_upgrade.sql

# 更新代码
git pull origin main

# 重新构建和部署
./build.sh
./deploy.sh
```

### 回滚操作
```bash
# 回滚到上一个版本
git checkout <previous_commit>
./build.sh
./deploy.sh --restart
```

## 环境变量模板

创建 `.env` 文件：
```bash
# MySQL配置
MYSQL_ROOT_PASSWORD=Cll@123?
MYSQL_DATABASE=model
MYSQL_USER=usermanager
MYSQL_PASSWORD=usermanager123
MYSQL_PORT=3306

# 应用配置
SPRING_PROFILES_ACTIVE=prod
JWT_SECRET=mySecretKey1234567890123456789012345678901234567890123456789012345678901234567890

# 端口配置
FRONTEND_PORT=80
BACKEND_PORT=8080
```

## 联系支持

如果遇到问题，请：
1. 查看日志文件
2. 检查配置文件
3. 参考故障排除指南
4. 联系技术支持

---

**注意**: 生产环境部署前，请务必：
- 修改所有默认密码
- 配置适当的安全策略
- 进行充分的测试
- 建立监控和备份机制
