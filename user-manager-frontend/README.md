# UserManager Frontend

UserManager 系统的 Vue.js 前端管理界面，提供完整的用户管理和统计分析功能。

## 功能特性

### 🔐 超级管理员登录
- 只有超级用户 `root` 可以登录系统
- 默认账号：`root`，密码：`admin123`
- JWT Token 认证机制

### 📊 数据统计分析
- **用户总览统计**：总用户数、活跃用户、非活跃用户
- **VIP用户统计**：总VIP用户、有效VIP、过期VIP、非VIP用户
- **VIP类型分布**：Premium、Basic、Enterprise 用户分布
- **可视化图表**：饼图、柱状图、进度条等多种图表展示

### 👥 用户管理
- 查看所有用户列表
- 用户详细信息展示（ID、激活码、代理、状态、VIP信息等）
- 用户删除功能
- 分页显示和数据刷新

### 🧪 接口测试工具
- **用户登录测试**：测试激活码登录功能
- **用户信息查询**：测试 whoami 接口
- **用户登出测试**：测试登出功能
- **健康检查**：测试系统健康状态
- **完整测试流程**：一键运行所有测试

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **Vue Router 4** - 官方路由管理器
- **Vuex 4** - 状态管理
- **Element Plus** - Vue 3 UI 组件库
- **ECharts** - 数据可视化图表库
- **Axios** - HTTP 客户端
- **Sass** - CSS 预处理器

## 项目结构

```
user-manager-frontend/
├── public/
│   └── index.html          # HTML 模板
├── src/
│   ├── components/         # 公共组件
│   ├── router/            # 路由配置
│   │   └── index.js
│   ├── store/             # Vuex 状态管理
│   │   └── index.js
│   ├── utils/             # 工具函数
│   │   └── api.js         # API 请求封装
│   ├── views/             # 页面组件
│   │   ├── Login.vue      # 登录页面
│   │   ├── Dashboard.vue  # 主布局
│   │   ├── Overview.vue   # 概览页面
│   │   ├── Users.vue      # 用户管理
│   │   ├── Statistics.vue # 统计分析
│   │   └── UserTest.vue   # 接口测试
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── package.json           # 项目配置
├── vue.config.js          # Vue CLI 配置
└── README.md              # 项目说明
```

## 安装和运行

### 前置要求
- Node.js 16+ 
- npm 或 yarn
- 后端 UserManager API 服务运行在 `http://localhost:8081`

### 安装依赖
```bash
cd user-manager-frontend
npm install
```

### 开发环境运行
```bash
npm run serve
```

访问 `http://localhost:3000` 即可使用系统。

### 生产环境构建
```bash
npm run build
```

## 使用说明

### 1. 登录系统
- 访问 `http://localhost:3000`
- 使用默认账号 `root` 和密码 `admin123` 登录
- 登录成功后自动跳转到管理界面

### 2. 概览页面
- 查看系统整体统计数据
- 用户状态和VIP类型的可视化图表
- 系统运行状态信息

### 3. 用户管理
- 查看所有用户的详细信息
- 支持分页浏览
- 可以删除指定用户
- 实时刷新数据

### 4. 统计分析
- 详细的用户统计数据
- 多种图表展示用户分布情况
- 百分比分析和趋势展示

### 5. 接口测试
- 测试所有用户相关的API接口
- 支持单独测试和完整流程测试
- 实时显示测试结果

## API 接口

前端调用的主要 API 接口：

### 管理员接口
- `POST /admin/login` - 管理员登录
- `GET /admin/statistics` - 获取统计信息
- `GET /admin/users` - 获取用户列表
- `DELETE /admin/users/{userId}` - 删除用户

### 用户接口
- `GET /health` - 健康检查
- `POST /users/card-login` - 用户登录
- `POST /users/whoami` - 获取用户信息
- `POST /users/logout` - 用户登出

## 配置说明

### 代理配置
在 `vue.config.js` 中配置了开发环境的代理：
```javascript
devServer: {
  proxy: {
    '/api': {
      target: 'http://localhost:8081',
      changeOrigin: true
    }
  }
}
```

### 路由守卫
在 `src/router/index.js` 中配置了路由守卫，确保只有登录用户才能访问管理页面。

## 开发说明

### 状态管理
使用 Vuex 管理全局状态：
- 用户认证状态
- 用户列表数据
- 统计信息数据

### 请求拦截
在 `src/utils/api.js` 中配置了请求和响应拦截器：
- 自动添加认证 Token
- 统一错误处理
- 自动处理登录过期

### 组件设计
- 使用 Vue 3 Composition API
- Element Plus 组件库提供 UI 支持
- ECharts 提供数据可视化

## 注意事项

1. 确保后端 API 服务正常运行
2. 默认管理员账号密码为 `root/admin123`
3. 系统使用 JWT Token 进行认证
4. 所有管理功能都需要超级管理员权限
5. 前端会自动处理 Token 过期和重新登录
