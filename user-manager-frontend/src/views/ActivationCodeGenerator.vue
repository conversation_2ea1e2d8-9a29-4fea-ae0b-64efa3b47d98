<template>
  <div class="activation-code-generator">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>激活码生成器</span>
          <el-button type="primary" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <!-- 激活码格式说明 -->
      <el-alert
        title="激活码格式说明"
        type="info"
        :closable="false"
        style="margin-bottom: 20px;"
      >
        <template #default>
          <p>激活码格式：<strong>ABCDE-12345-FGHIJ-67890-KLMNO</strong></p>
          <ul>
            <li>5个字符组成的组，由连字符分隔</li>
            <li>包含大写字母和数字</li>
            <li>总长度为25个字符（5组×5字符）</li>
          </ul>
        </template>
      </el-alert>

      <!-- 生成表单 -->
      <el-form 
        :model="generateForm" 
        :rules="generateRules" 
        ref="generateFormRef"
        label-width="120px"
        style="max-width: 600px;"
      >
        <el-form-item label="过期天数" prop="expireDays">
          <el-input-number
            v-model="generateForm.expireDays"
            :min="1"
            :max="3650"
            placeholder="请输入过期天数"
            style="width: 200px;"
          />
          <span style="margin-left: 10px; color: #666;">天</span>
        </el-form-item>

        <el-form-item label="代理标识" prop="agent">
          <el-input
            v-model="generateForm.agent"
            placeholder="请输入代理标识"
            style="width: 200px;"
          />
        </el-form-item>

        <el-form-item label="VIP产品类型" prop="product">
          <el-select
            v-model="generateForm.product"
            placeholder="请选择VIP产品类型"
            style="width: 200px;"
          >
            <el-option label="Premium" value="premium" />
            <el-option label="Basic" value="basic" />
            <el-option label="Enterprise" value="enterprise" />
          </el-select>
        </el-form-item>

        <el-form-item label="权限等级" prop="power">
          <el-input-number
            v-model="generateForm.power"
            :min="1"
            :max="10"
            style="width: 200px;"
          />
        </el-form-item>

        <el-form-item label="总积分" prop="score">
          <el-input-number
            v-model="generateForm.score"
            :min="0"
            :max="100000"
            style="width: 200px;"
          />
        </el-form-item>

        <el-form-item label="每日积分额度" prop="dayScore">
          <el-input-number
            v-model="generateForm.dayScore"
            :min="0"
            :max="1000"
            :precision="1"
            style="width: 200px;"
          />
        </el-form-item>

        <el-form-item>
          <el-button 
            type="primary" 
            @click="generateActivationCode"
            :loading="generateLoading"
            size="large"
          >
            <el-icon><Plus /></el-icon>
            生成激活码
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 生成结果 -->
      <div v-if="generatedResult" class="result-section">
        <el-divider content-position="left">生成结果</el-divider>
        
        <el-alert
          :title="`激活码生成成功！`"
          type="success"
          :closable="false"
          style="margin-bottom: 20px;"
        >
          <template #default>
            <div class="activation-code-display">
              <h3>激活码：{{ generatedResult.activationCode }}</h3>
              <el-button 
                type="primary" 
                size="small" 
                @click="copyToClipboard(generatedResult.activationCode)"
              >
                复制激活码
              </el-button>
            </div>
          </template>
        </el-alert>

        <el-descriptions title="用户信息" :column="2" border>
          <el-descriptions-item label="用户ID">
            {{ generatedResult.user.id }}
          </el-descriptions-item>
          <el-descriptions-item label="代理标识">
            {{ generatedResult.user.agent }}
          </el-descriptions-item>
          <el-descriptions-item label="用户状态">
            <el-tag :type="generatedResult.user.status === 'active' ? 'success' : 'danger'">
              {{ generatedResult.user.status === 'active' ? '活跃' : '非活跃' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(generatedResult.user.createdAt) }}
          </el-descriptions-item>
        </el-descriptions>

        <el-descriptions title="VIP信息" :column="2" border style="margin-top: 20px;">
          <el-descriptions-item label="产品类型">
            <el-tag :type="getVipTagType(generatedResult.vipInfo.product)">
              {{ generatedResult.vipInfo.product }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="权限等级">
            {{ generatedResult.vipInfo.power }}
          </el-descriptions-item>
          <el-descriptions-item label="过期时间">
            {{ formatDate(generatedResult.vipInfo.expire_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="剩余天数">
            {{ Math.ceil((generatedResult.vipInfo.expire_at - Date.now()) / (24 * 60 * 60 * 1000)) }} 天
          </el-descriptions-item>
          <el-descriptions-item label="总积分">
            {{ generatedResult.vipInfo.score }}
          </el-descriptions-item>
          <el-descriptions-item label="每日积分额度">
            {{ generatedResult.vipInfo.day_score }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import api from '@/utils/api'

export default {
  name: 'ActivationCodeGenerator',
  components: {
    Plus,
    Refresh
  },
  setup() {
    const generateFormRef = ref(null)
    const loading = ref(false)
    const generateLoading = ref(false)
    const generatedResult = ref(null)

    const generateForm = reactive({
      expireDays: 365,
      agent: 'main',
      product: 'premium',
      power: 5,
      score: 1000,
      dayScore: 100.0
    })

    const generateRules = {
      expireDays: [
        { required: true, message: '请输入过期天数', trigger: 'blur' },
        { type: 'number', min: 1, max: 3650, message: '过期天数必须在1-3650天之间', trigger: 'blur' }
      ],
      agent: [
        { required: true, message: '请输入代理标识', trigger: 'blur' }
      ],
      product: [
        { required: true, message: '请选择VIP产品类型', trigger: 'change' }
      ],
      power: [
        { required: true, message: '请输入权限等级', trigger: 'blur' },
        { type: 'number', min: 1, max: 10, message: '权限等级必须在1-10之间', trigger: 'blur' }
      ],
      score: [
        { required: true, message: '请输入总积分', trigger: 'blur' },
        { type: 'number', min: 0, message: '总积分不能为负数', trigger: 'blur' }
      ],
      dayScore: [
        { required: true, message: '请输入每日积分额度', trigger: 'blur' },
        { type: 'number', min: 0, message: '每日积分额度不能为负数', trigger: 'blur' }
      ]
    }

    const generateActivationCode = async () => {
      try {
        await generateFormRef.value.validate()
        generateLoading.value = true

        const response = await api.post('/users/generate-activation-code', generateForm)
        generatedResult.value = response.data.data

        ElMessage.success('激活码生成成功！')
      } catch (error) {
        console.error('Generate activation code error:', error)
        if (error.response && error.response.data && error.response.data.msg) {
          ElMessage.error(error.response.data.msg)
        } else {
          ElMessage.error('生成激活码失败')
        }
      } finally {
        generateLoading.value = false
      }
    }

    const resetForm = () => {
      generateFormRef.value.resetFields()
      generatedResult.value = null
    }

    const refreshData = () => {
      generatedResult.value = null
      ElMessage.success('页面已刷新')
    }

    const copyToClipboard = async (text) => {
      try {
        await navigator.clipboard.writeText(text)
        ElMessage.success('激活码已复制到剪贴板')
      } catch (error) {
        console.error('Copy failed:', error)
        ElMessage.error('复制失败，请手动复制')
      }
    }

    const formatDate = (timestamp) => {
      if (!timestamp) return '-'
      const date = new Date(timestamp)
      return date.toLocaleString('zh-CN')
    }

    const getVipTagType = (product) => {
      switch (product) {
        case 'premium': return 'success'
        case 'basic': return 'warning'
        case 'enterprise': return 'danger'
        default: return 'info'
      }
    }

    onMounted(() => {
      // 页面加载时的初始化操作
    })

    return {
      generateFormRef,
      loading,
      generateLoading,
      generatedResult,
      generateForm,
      generateRules,
      generateActivationCode,
      resetForm,
      refreshData,
      copyToClipboard,
      formatDate,
      getVipTagType
    }
  }
}
</script>

<style scoped>
.activation-code-generator {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-section {
  margin-top: 30px;
}

.activation-code-display {
  display: flex;
  align-items: center;
  gap: 15px;
}

.activation-code-display h3 {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 18px;
  color: #409eff;
}
</style>
