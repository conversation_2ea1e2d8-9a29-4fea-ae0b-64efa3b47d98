<template>
  <div class="user-test">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户登录测试</span>
          </template>
          
          <el-form :model="loginForm" label-width="100px">
            <el-form-item label="激活码">
              <el-input 
                v-model="loginForm.card" 
                placeholder="请输入激活码"
              />
            </el-form-item>
            <el-form-item label="代理">
              <el-input 
                v-model="loginForm.agent" 
                placeholder="请输入代理标识"
              />
            </el-form-item>
            <el-form-item>
              <el-button 
                type="primary" 
                @click="testLogin"
                :loading="loginLoading"
              >
                测试登录
              </el-button>
              <el-button @click="clearLoginResult">清空结果</el-button>
            </el-form-item>
          </el-form>
          
          <div v-if="loginResult" class="result-section">
            <h4>登录结果：</h4>
            <el-alert
              :title="loginResult.success ? '登录成功' : '登录失败'"
              :type="loginResult.success ? 'success' : 'error'"
              :description="loginResult.message"
              show-icon
            />
            <div v-if="loginResult.data" class="result-data">
              <pre>{{ JSON.stringify(loginResult.data, null, 2) }}</pre>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户信息查询测试</span>
          </template>
          
          <el-form :model="whoamiForm" label-width="100px">
            <el-form-item label="Token">
              <el-input 
                v-model="whoamiForm.token" 
                placeholder="请输入用户Token"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
            <el-form-item>
              <el-button 
                type="primary" 
                @click="testWhoami"
                :loading="whoamiLoading"
              >
                查询用户信息
              </el-button>
              <el-button @click="clearWhoamiResult">清空结果</el-button>
            </el-form-item>
          </el-form>
          
          <div v-if="whoamiResult" class="result-section">
            <h4>查询结果：</h4>
            <el-alert
              :title="whoamiResult.success ? '查询成功' : '查询失败'"
              :type="whoamiResult.success ? 'success' : 'error'"
              :description="whoamiResult.message"
              show-icon
            />
            <div v-if="whoamiResult.data" class="result-data">
              <pre>{{ JSON.stringify(whoamiResult.data, null, 2) }}</pre>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户登出测试</span>
          </template>
          
          <el-form :model="logoutForm" label-width="100px">
            <el-form-item label="Token">
              <el-input 
                v-model="logoutForm.token" 
                placeholder="请输入用户Token"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
            <el-form-item>
              <el-button 
                type="primary" 
                @click="testLogout"
                :loading="logoutLoading"
              >
                测试登出
              </el-button>
              <el-button @click="clearLogoutResult">清空结果</el-button>
            </el-form-item>
          </el-form>
          
          <div v-if="logoutResult" class="result-section">
            <h4>登出结果：</h4>
            <el-alert
              :title="logoutResult.success ? '登出成功' : '登出失败'"
              :type="logoutResult.success ? 'success' : 'error'"
              :description="logoutResult.message"
              show-icon
            />
            <div v-if="logoutResult.data" class="result-data">
              <pre>{{ JSON.stringify(logoutResult.data, null, 2) }}</pre>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>健康检查测试</span>
          </template>
          
          <el-form label-width="100px">
            <el-form-item>
              <el-button 
                type="primary" 
                @click="testHealth"
                :loading="healthLoading"
              >
                检查系统健康状态
              </el-button>
              <el-button @click="clearHealthResult">清空结果</el-button>
            </el-form-item>
          </el-form>
          
          <div v-if="healthResult" class="result-section">
            <h4>健康检查结果：</h4>
            <el-alert
              :title="healthResult.success ? '系统正常' : '系统异常'"
              :type="healthResult.success ? 'success' : 'error'"
              :description="healthResult.message"
              show-icon
            />
            <div v-if="healthResult.data" class="result-data">
              <pre>{{ JSON.stringify(healthResult.data, null, 2) }}</pre>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>快速测试</span>
          </template>
          
          <div class="quick-test">
            <el-button 
              type="success" 
              @click="runFullTest"
              :loading="fullTestLoading"
            >
              运行完整测试流程
            </el-button>
            <el-button @click="clearAllResults">清空所有结果</el-button>
          </div>
          
          <div v-if="fullTestResult" class="result-section">
            <h4>完整测试结果：</h4>
            <div v-for="(step, index) in fullTestResult" :key="index" class="test-step">
              <el-alert
                :title="step.name"
                :type="step.success ? 'success' : 'error'"
                :description="step.message"
                show-icon
              />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 新激活码登录逻辑测试 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>新激活码登录逻辑测试</span>
          </template>

          <el-row :gutter="20">
            <!-- 不存在的激活码测试 -->
            <el-col :span="8">
              <h4>测试1：不存在的激活码</h4>
              <el-form :model="nonExistentCodeForm" label-width="80px">
                <el-form-item label="激活码">
                  <el-input
                    v-model="nonExistentCodeForm.card"
                    placeholder="不存在的激活码"
                  />
                </el-form-item>
                <el-form-item label="代理">
                  <el-input
                    v-model="nonExistentCodeForm.agent"
                    placeholder="代理标识"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    @click="testNonExistentCode"
                    :loading="nonExistentCodeLoading"
                    size="small"
                  >
                    测试登录
                  </el-button>
                </el-form-item>
              </el-form>

              <div v-if="nonExistentCodeResult" class="result-section">
                <el-alert
                  :title="nonExistentCodeResult.success ? '登录成功' : '登录失败'"
                  :type="nonExistentCodeResult.success ? 'success' : 'error'"
                  :description="nonExistentCodeResult.message"
                  show-icon
                  size="small"
                />
              </div>
            </el-col>

            <!-- 过期激活码测试 -->
            <el-col :span="8">
              <h4>测试2：过期激活码</h4>
              <el-form :model="expiredCodeForm" label-width="80px">
                <el-form-item label="激活码">
                  <el-input
                    v-model="expiredCodeForm.card"
                    placeholder="过期的激活码"
                  />
                </el-form-item>
                <el-form-item label="代理">
                  <el-input
                    v-model="expiredCodeForm.agent"
                    placeholder="代理标识"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    @click="testExpiredCode"
                    :loading="expiredCodeLoading"
                    size="small"
                  >
                    测试登录
                  </el-button>
                </el-form-item>
              </el-form>

              <div v-if="expiredCodeResult" class="result-section">
                <el-alert
                  :title="expiredCodeResult.success ? '登录成功' : '登录失败'"
                  :type="expiredCodeResult.success ? 'success' : 'error'"
                  :description="expiredCodeResult.message"
                  show-icon
                  size="small"
                />
              </div>
            </el-col>

            <!-- 有效激活码测试 -->
            <el-col :span="8">
              <h4>测试3：有效激活码</h4>
              <el-form :model="validCodeForm" label-width="80px">
                <el-form-item label="激活码">
                  <el-input
                    v-model="validCodeForm.card"
                    placeholder="有效的激活码"
                  />
                </el-form-item>
                <el-form-item label="代理">
                  <el-input
                    v-model="validCodeForm.agent"
                    placeholder="代理标识"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    @click="testValidCode"
                    :loading="validCodeLoading"
                    size="small"
                  >
                    测试登录
                  </el-button>
                </el-form-item>
              </el-form>

              <div v-if="validCodeResult" class="result-section">
                <el-alert
                  :title="validCodeResult.success ? '登录成功' : '登录失败'"
                  :type="validCodeResult.success ? 'success' : 'error'"
                  :description="validCodeResult.message"
                  show-icon
                  size="small"
                />
              </div>
            </el-col>
          </el-row>

          <el-divider />

          <div style="text-align: center;">
            <el-button
              type="success"
              @click="runNewLoginTests"
              :loading="newLoginTestsLoading"
            >
              运行所有新登录逻辑测试
            </el-button>
            <el-button @click="clearNewLoginResults">清空所有结果</el-button>
          </div>

          <div v-if="newLoginTestsResult" class="result-section" style="margin-top: 20px;">
            <h4>综合测试结果：</h4>
            <div v-for="(step, index) in newLoginTestsResult" :key="index" class="test-step">
              <el-alert
                :title="step.name"
                :type="step.success ? 'success' : 'error'"
                :description="step.message"
                show-icon
                style="margin-bottom: 10px;"
              />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref } from 'vue'
import api from '@/utils/api'
import axios from 'axios'
import { ElMessage } from 'element-plus'

export default {
  name: 'UserTest',
  setup() {
    // 登录测试
    const loginForm = ref({
      card: 'test123456789',
      agent: 'main'
    })
    const loginLoading = ref(false)
    const loginResult = ref(null)
    
    // 用户信息查询测试
    const whoamiForm = ref({
      token: ''
    })
    const whoamiLoading = ref(false)
    const whoamiResult = ref(null)
    
    // 登出测试
    const logoutForm = ref({
      token: ''
    })
    const logoutLoading = ref(false)
    const logoutResult = ref(null)
    
    // 健康检查测试
    const healthLoading = ref(false)
    const healthResult = ref(null)
    
    // 完整测试
    const fullTestLoading = ref(false)
    const fullTestResult = ref(null)

    // 新激活码登录逻辑测试
    const nonExistentCodeForm = ref({
      card: 'NONEX-12345-ISTENT-67890-CODES',
      agent: 'main'
    })
    const nonExistentCodeLoading = ref(false)
    const nonExistentCodeResult = ref(null)

    const expiredCodeForm = ref({
      card: 'EXPIR-12345-EDCOD-67890-ETEST',
      agent: 'main'
    })
    const expiredCodeLoading = ref(false)
    const expiredCodeResult = ref(null)

    const validCodeForm = ref({
      card: 'VALID-12345-CODES-67890-TESTS',
      agent: 'main'
    })
    const validCodeLoading = ref(false)
    const validCodeResult = ref(null)

    const newLoginTestsLoading = ref(false)
    const newLoginTestsResult = ref(null)
    
    const testLogin = async () => {
      loginLoading.value = true
      try {
        const response = await api.post('/users/card-login', loginForm.value)
        loginResult.value = {
          success: true,
          message: '登录成功',
          data: response.data.data
        }
        
        // 自动填充token到其他测试表单
        if (response.data.data.token) {
          whoamiForm.value.token = response.data.data.token
          logoutForm.value.token = response.data.data.token
        }
        
        ElMessage.success('登录测试成功')
      } catch (error) {
        loginResult.value = {
          success: false,
          message: error.message || '登录失败',
          data: error.response?.data
        }
      } finally {
        loginLoading.value = false
      }
    }
    
    const testWhoami = async () => {
      if (!whoamiForm.value.token) {
        ElMessage.warning('请先输入Token')
        return
      }
      
      whoamiLoading.value = true
      try {
        const response = await axios.post('/api/users/whoami', {}, {
          headers: {
            'X-Auth-Token': whoamiForm.value.token
          }
        })
        whoamiResult.value = {
          success: true,
          message: '查询成功',
          data: response.data.data
        }
        ElMessage.success('用户信息查询成功')
      } catch (error) {
        whoamiResult.value = {
          success: false,
          message: error.message || '查询失败',
          data: error.response?.data
        }
      } finally {
        whoamiLoading.value = false
      }
    }
    
    const testLogout = async () => {
      if (!logoutForm.value.token) {
        ElMessage.warning('请先输入Token')
        return
      }
      
      logoutLoading.value = true
      try {
        const response = await axios.post('/api/users/logout', {}, {
          headers: {
            'X-Auth-Token': logoutForm.value.token
          }
        })
        logoutResult.value = {
          success: true,
          message: '登出成功',
          data: response.data.data
        }
        ElMessage.success('登出测试成功')
      } catch (error) {
        logoutResult.value = {
          success: false,
          message: error.message || '登出失败',
          data: error.response?.data
        }
      } finally {
        logoutLoading.value = false
      }
    }
    
    const testHealth = async () => {
      healthLoading.value = true
      try {
        const response = await api.get('/health')
        healthResult.value = {
          success: true,
          message: '系统健康状态正常',
          data: response.data.data
        }
        ElMessage.success('健康检查成功')
      } catch (error) {
        healthResult.value = {
          success: false,
          message: error.message || '健康检查失败',
          data: error.response?.data
        }
      } finally {
        healthLoading.value = false
      }
    }
    
    const runFullTest = async () => {
      fullTestLoading.value = true
      fullTestResult.value = []
      
      try {
        // 1. 健康检查
        try {
          await api.get('/health')
          fullTestResult.value.push({
            name: '1. 健康检查',
            success: true,
            message: '系统健康状态正常'
          })
        } catch (error) {
          fullTestResult.value.push({
            name: '1. 健康检查',
            success: false,
            message: '健康检查失败: ' + error.message
          })
        }
        
        // 2. 用户登录
        let userToken = null
        try {
          const response = await api.post('/users/card-login', {
            card: 'fulltest' + Date.now(),
            agent: 'main'
          })
          userToken = response.data.data.token
          fullTestResult.value.push({
            name: '2. 用户登录',
            success: true,
            message: '用户登录成功'
          })
        } catch (error) {
          fullTestResult.value.push({
            name: '2. 用户登录',
            success: false,
            message: '用户登录失败: ' + error.message
          })
        }
        
        // 3. 用户信息查询
        if (userToken) {
          try {
            await axios.post('/api/users/whoami', {}, {
              headers: { 'X-Auth-Token': userToken }
            })
            fullTestResult.value.push({
              name: '3. 用户信息查询',
              success: true,
              message: '用户信息查询成功'
            })
          } catch (error) {
            fullTestResult.value.push({
              name: '3. 用户信息查询',
              success: false,
              message: '用户信息查询失败: ' + error.message
            })
          }
          
          // 4. 用户登出
          try {
            await axios.post('/api/users/logout', {}, {
              headers: { 'X-Auth-Token': userToken }
            })
            fullTestResult.value.push({
              name: '4. 用户登出',
              success: true,
              message: '用户登出成功'
            })
          } catch (error) {
            fullTestResult.value.push({
              name: '4. 用户登出',
              success: false,
              message: '用户登出失败: ' + error.message
            })
          }
        }
        
        ElMessage.success('完整测试流程执行完成')
      } catch (error) {
        ElMessage.error('测试流程执行失败')
      } finally {
        fullTestLoading.value = false
      }
    }
    
    const clearLoginResult = () => { loginResult.value = null }
    const clearWhoamiResult = () => { whoamiResult.value = null }
    const clearLogoutResult = () => { logoutResult.value = null }
    const clearHealthResult = () => { healthResult.value = null }
    const clearAllResults = () => {
      loginResult.value = null
      whoamiResult.value = null
      logoutResult.value = null
      healthResult.value = null
      fullTestResult.value = null
    }

    // 新激活码登录逻辑测试方法
    const testNonExistentCode = async () => {
      nonExistentCodeLoading.value = true
      try {
        const response = await api.post('/users/card-login', nonExistentCodeForm.value)
        nonExistentCodeResult.value = {
          success: true,
          message: '意外成功：激活码不应该存在',
          data: response.data.data
        }
      } catch (error) {
        nonExistentCodeResult.value = {
          success: false,
          message: error.response?.data?.msg || error.message || '测试失败',
          data: error.response?.data
        }
      } finally {
        nonExistentCodeLoading.value = false
      }
    }

    const testExpiredCode = async () => {
      expiredCodeLoading.value = true
      try {
        const response = await api.post('/users/card-login', expiredCodeForm.value)
        expiredCodeResult.value = {
          success: true,
          message: '意外成功：激活码应该已过期',
          data: response.data.data
        }
      } catch (error) {
        expiredCodeResult.value = {
          success: false,
          message: error.response?.data?.msg || error.message || '测试失败',
          data: error.response?.data
        }
      } finally {
        expiredCodeLoading.value = false
      }
    }

    const testValidCode = async () => {
      validCodeLoading.value = true
      try {
        const response = await api.post('/users/card-login', validCodeForm.value)
        validCodeResult.value = {
          success: true,
          message: '登录成功',
          data: response.data.data
        }
      } catch (error) {
        validCodeResult.value = {
          success: false,
          message: error.response?.data?.msg || error.message || '测试失败',
          data: error.response?.data
        }
      } finally {
        validCodeLoading.value = false
      }
    }

    const runNewLoginTests = async () => {
      newLoginTestsLoading.value = true
      const results = []

      try {
        // 测试1：不存在的激活码
        try {
          await api.post('/users/card-login', nonExistentCodeForm.value)
          results.push({
            name: '测试1：不存在的激活码',
            success: false,
            message: '意外成功：激活码不应该存在'
          })
        } catch (error) {
          const message = error.response?.data?.msg || error.message
          if (message.includes('激活码不存在')) {
            results.push({
              name: '测试1：不存在的激活码',
              success: true,
              message: '正确返回：' + message
            })
          } else {
            results.push({
              name: '测试1：不存在的激活码',
              success: false,
              message: '错误信息不符合预期：' + message
            })
          }
        }

        // 测试2：过期激活码（需要先生成一个过期的激活码）
        results.push({
          name: '测试2：过期激活码',
          success: true,
          message: '需要先生成过期激活码进行测试'
        })

        // 测试3：有效激活码（需要先生成一个有效的激活码）
        results.push({
          name: '测试3：有效激活码',
          success: true,
          message: '需要先生成有效激活码进行测试'
        })

        newLoginTestsResult.value = results
        ElMessage.success('新登录逻辑测试完成')
      } catch (error) {
        console.error('New login tests error:', error)
        ElMessage.error('测试过程中发生错误')
      } finally {
        newLoginTestsLoading.value = false
      }
    }

    const clearNewLoginResults = () => {
      nonExistentCodeResult.value = null
      expiredCodeResult.value = null
      validCodeResult.value = null
      newLoginTestsResult.value = null
    }
    
    return {
      loginForm,
      loginLoading,
      loginResult,
      whoamiForm,
      whoamiLoading,
      whoamiResult,
      logoutForm,
      logoutLoading,
      logoutResult,
      healthLoading,
      healthResult,
      fullTestLoading,
      fullTestResult,
      // 新激活码登录逻辑测试
      nonExistentCodeForm,
      nonExistentCodeLoading,
      nonExistentCodeResult,
      expiredCodeForm,
      expiredCodeLoading,
      expiredCodeResult,
      validCodeForm,
      validCodeLoading,
      validCodeResult,
      newLoginTestsLoading,
      newLoginTestsResult,
      // 方法
      testLogin,
      testWhoami,
      testLogout,
      testHealth,
      runFullTest,
      testNonExistentCode,
      testExpiredCode,
      testValidCode,
      runNewLoginTests,
      clearLoginResult,
      clearWhoamiResult,
      clearLogoutResult,
      clearHealthResult,
      clearAllResults,
      clearNewLoginResults
    }
  }
}
</script>

<style scoped>
.user-test {
  padding: 0;
}

.result-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.result-section h4 {
  margin-bottom: 10px;
  color: #333;
}

.result-data {
  margin-top: 10px;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.quick-test {
  margin-bottom: 20px;
}

.test-step {
  margin-bottom: 10px;
}
</style>
