<template>
  <div class="users">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
          <el-button type="primary" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      
      <div class="table-container">
        <el-table 
          :data="users" 
          v-loading="loading"
          style="width: 100%"
          stripe
          border
        >
          <el-table-column prop="userId" label="用户ID" width="180" />
          <el-table-column prop="activationCode" label="激活码" width="200" />
          <el-table-column prop="agent" label="代理" width="100" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                {{ scope.row.status === 'active' ? '活跃' : '非活跃' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="VIP信息" width="200">
            <template #default="scope">
              <div v-if="scope.row.vip">
                <el-tag :type="getVipTagType(scope.row.vip.product)">
                  {{ scope.row.vip.product }}
                </el-tag>
                <div class="vip-details">
                  <small>积分: {{ scope.row.vip.score }}</small><br>
                  <small :class="{ 'expired': !scope.row.vip.valid }">
                    {{ scope.row.vip.valid ? '有效' : '已过期' }}
                  </small>
                </div>
              </div>
              <el-tag v-else type="info">非VIP</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间" width="180">
            <template #default="scope">
              {{ formatDate(scope.row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column prop="lastLoginAt" label="最后登录" width="180">
            <template #default="scope">
              {{ formatDate(scope.row.lastLoginAt) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button 
                type="danger" 
                size="small"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="filteredUsers.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'Users',
  setup() {
    const store = useStore()
    const loading = ref(false)
    const currentPage = ref(1)
    const pageSize = ref(20)
    
    const allUsers = computed(() => store.getters.allUsers)
    
    const filteredUsers = computed(() => allUsers.value)
    
    const users = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filteredUsers.value.slice(start, end)
    })
    
    const getVipTagType = (product) => {
      const typeMap = {
        'premium': 'warning',
        'basic': 'info',
        'enterprise': 'success'
      }
      return typeMap[product] || 'info'
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-CN')
    }
    
    const refreshData = async () => {
      loading.value = true
      try {
        await store.dispatch('fetchUsers')
        ElMessage.success('数据刷新成功')
      } catch (error) {
        console.error('Failed to fetch users:', error)
      } finally {
        loading.value = false
      }
    }
    
    const handleDelete = async (user) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除用户 "${user.userId}" 吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        loading.value = true
        await store.dispatch('deleteUser', user.userId)
        ElMessage.success('用户删除成功')
      } catch (error) {
        if (error !== 'cancel') {
          console.error('Failed to delete user:', error)
        }
      } finally {
        loading.value = false
      }
    }
    
    const handleSizeChange = (val) => {
      pageSize.value = val
      currentPage.value = 1
    }
    
    const handleCurrentChange = (val) => {
      currentPage.value = val
    }
    
    onMounted(() => {
      refreshData()
    })
    
    return {
      users,
      loading,
      currentPage,
      pageSize,
      filteredUsers,
      getVipTagType,
      formatDate,
      refreshData,
      handleDelete,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style scoped>
.users {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-container {
  margin-top: 20px;
}

.vip-details {
  margin-top: 5px;
  line-height: 1.2;
}

.vip-details small {
  color: #666;
}

.vip-details .expired {
  color: #f56c6c;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
