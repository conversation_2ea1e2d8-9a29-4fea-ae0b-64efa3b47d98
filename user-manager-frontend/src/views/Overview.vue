<template>
  <div class="overview">
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ statistics?.totalUsers || 0 }}</h3>
              <p>总用户数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ statistics?.activeUsers || 0 }}</h3>
              <p>活跃用户</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon vip">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ statistics?.validVipUsers || 0 }}</h3>
              <p>有效VIP用户</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon expired">
              <el-icon><WarningFilled /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ statistics?.expiredVipUsers || 0 }}</h3>
              <p>过期VIP用户</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户状态分布</span>
          </template>
          <v-chart 
            class="chart" 
            :option="userStatusOption" 
            v-if="statistics"
          />
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>VIP类型分布</span>
          </template>
          <v-chart 
            class="chart" 
            :option="vipTypeOption" 
            v-if="statistics"
          />
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>系统状态</span>
          </template>
          <div class="system-status">
            <el-descriptions :column="3" border>
              <el-descriptions-item label="系统状态">
                <el-tag type="success">运行正常</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="API服务">
                <el-tag type="success">在线</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="数据库">
                <el-tag type="success">连接正常</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="总用户数">
                {{ statistics?.totalUsers || 0 }}
              </el-descriptions-item>
              <el-descriptions-item label="VIP用户占比">
                {{ vipPercentage }}%
              </el-descriptions-item>
              <el-descriptions-item label="活跃用户占比">
                {{ activePercentage }}%
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { computed, onMounted } from 'vue'
import { useStore } from 'vuex'

export default {
  name: 'Overview',
  setup() {
    const store = useStore()
    
    const statistics = computed(() => store.getters.userStatistics)
    
    const vipPercentage = computed(() => {
      if (!statistics.value || statistics.value.totalUsers === 0) return 0
      return Math.round((statistics.value.totalVipUsers / statistics.value.totalUsers) * 100)
    })
    
    const activePercentage = computed(() => {
      if (!statistics.value || statistics.value.totalUsers === 0) return 0
      return Math.round((statistics.value.activeUsers / statistics.value.totalUsers) * 100)
    })
    
    const userStatusOption = computed(() => ({
      title: {
        text: '用户状态',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [
        {
          name: '用户状态',
          type: 'pie',
          radius: '50%',
          data: [
            { value: statistics.value?.activeUsers || 0, name: '活跃用户' },
            { value: statistics.value?.inactiveUsers || 0, name: '非活跃用户' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }))
    
    const vipTypeOption = computed(() => ({
      title: {
        text: 'VIP类型分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [
        {
          name: 'VIP类型',
          type: 'pie',
          radius: '50%',
          data: [
            { value: statistics.value?.premiumUsers || 0, name: 'Premium' },
            { value: statistics.value?.basicUsers || 0, name: 'Basic' },
            { value: statistics.value?.enterpriseUsers || 0, name: 'Enterprise' },
            { value: statistics.value?.nonVipUsers || 0, name: '非VIP' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }))
    
    const fetchData = async () => {
      try {
        await store.dispatch('fetchStatistics')
      } catch (error) {
        console.error('Failed to fetch statistics:', error)
      }
    }
    
    onMounted(() => {
      fetchData()
    })
    
    return {
      statistics,
      vipPercentage,
      activePercentage,
      userStatusOption,
      vipTypeOption
    }
  }
}
</script>

<style scoped>
.overview {
  padding: 0;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.vip {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.expired {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-info h3 {
  margin: 0 0 5px 0;
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.stat-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.charts-row {
  margin-bottom: 20px;
}

.chart {
  height: 300px;
}

.system-status {
  padding: 10px 0;
}
</style>
