<template>
  <div class="statistics">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>详细统计分析</span>
              <el-button type="primary" @click="refreshData">
                <el-icon><Refresh /></el-icon>
                刷新数据
              </el-button>
            </div>
          </template>
          
          <el-row :gutter="20" class="stats-overview">
            <el-col :span="8">
              <div class="stat-item">
                <h3>用户总览</h3>
                <el-descriptions :column="1" border>
                  <el-descriptions-item label="总用户数">
                    {{ statistics?.totalUsers || 0 }}
                  </el-descriptions-item>
                  <el-descriptions-item label="活跃用户">
                    {{ statistics?.activeUsers || 0 }}
                  </el-descriptions-item>
                  <el-descriptions-item label="非活跃用户">
                    {{ statistics?.inactiveUsers || 0 }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </el-col>
            
            <el-col :span="8">
              <div class="stat-item">
                <h3>VIP用户统计</h3>
                <el-descriptions :column="1" border>
                  <el-descriptions-item label="总VIP用户">
                    {{ statistics?.totalVipUsers || 0 }}
                  </el-descriptions-item>
                  <el-descriptions-item label="有效VIP">
                    <span class="valid-vip">{{ statistics?.validVipUsers || 0 }}</span>
                  </el-descriptions-item>
                  <el-descriptions-item label="过期VIP">
                    <span class="expired-vip">{{ statistics?.expiredVipUsers || 0 }}</span>
                  </el-descriptions-item>
                  <el-descriptions-item label="非VIP用户">
                    {{ statistics?.nonVipUsers || 0 }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </el-col>
            
            <el-col :span="8">
              <div class="stat-item">
                <h3>VIP类型分布</h3>
                <el-descriptions :column="1" border>
                  <el-descriptions-item label="Premium">
                    {{ statistics?.premiumUsers || 0 }}
                  </el-descriptions-item>
                  <el-descriptions-item label="Basic">
                    {{ statistics?.basicUsers || 0 }}
                  </el-descriptions-item>
                  <el-descriptions-item label="Enterprise">
                    {{ statistics?.enterpriseUsers || 0 }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户状态分布图</span>
          </template>
          <v-chart 
            class="chart" 
            :option="userStatusChartOption" 
            v-if="statistics"
          />
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>VIP状态分布图</span>
          </template>
          <v-chart 
            class="chart" 
            :option="vipStatusChartOption" 
            v-if="statistics"
          />
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>VIP类型柱状图</span>
          </template>
          <v-chart 
            class="chart" 
            :option="vipTypeBarOption" 
            v-if="statistics"
          />
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户比例分析</span>
          </template>
          <div class="percentage-analysis">
            <div class="percentage-item">
              <span class="label">活跃用户占比：</span>
              <el-progress 
                :percentage="activePercentage" 
                :color="progressColors"
              />
            </div>
            <div class="percentage-item">
              <span class="label">VIP用户占比：</span>
              <el-progress 
                :percentage="vipPercentage" 
                :color="progressColors"
              />
            </div>
            <div class="percentage-item">
              <span class="label">有效VIP占比：</span>
              <el-progress 
                :percentage="validVipPercentage" 
                :color="progressColors"
              />
            </div>
            <div class="percentage-item">
              <span class="label">过期VIP占比：</span>
              <el-progress 
                :percentage="expiredVipPercentage" 
                color="#f56c6c"
              />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { ElMessage } from 'element-plus'

export default {
  name: 'Statistics',
  setup() {
    const store = useStore()
    const loading = ref(false)
    
    const statistics = computed(() => store.getters.userStatistics)
    
    const activePercentage = computed(() => {
      if (!statistics.value || statistics.value.totalUsers === 0) return 0
      return Math.round((statistics.value.activeUsers / statistics.value.totalUsers) * 100)
    })
    
    const vipPercentage = computed(() => {
      if (!statistics.value || statistics.value.totalUsers === 0) return 0
      return Math.round((statistics.value.totalVipUsers / statistics.value.totalUsers) * 100)
    })
    
    const validVipPercentage = computed(() => {
      if (!statistics.value || statistics.value.totalVipUsers === 0) return 0
      return Math.round((statistics.value.validVipUsers / statistics.value.totalVipUsers) * 100)
    })
    
    const expiredVipPercentage = computed(() => {
      if (!statistics.value || statistics.value.totalVipUsers === 0) return 0
      return Math.round((statistics.value.expiredVipUsers / statistics.value.totalVipUsers) * 100)
    })
    
    const progressColors = [
      { color: '#f56c6c', percentage: 20 },
      { color: '#e6a23c', percentage: 40 },
      { color: '#5cb87a', percentage: 60 },
      { color: '#1989fa', percentage: 80 },
      { color: '#6f7ad3', percentage: 100 }
    ]
    
    const userStatusChartOption = computed(() => ({
      title: {
        text: '用户状态分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '用户状态',
          type: 'pie',
          radius: '50%',
          data: [
            { value: statistics.value?.activeUsers || 0, name: '活跃用户' },
            { value: statistics.value?.inactiveUsers || 0, name: '非活跃用户' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }))
    
    const vipStatusChartOption = computed(() => ({
      title: {
        text: 'VIP状态分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: 'VIP状态',
          type: 'pie',
          radius: '50%',
          data: [
            { value: statistics.value?.validVipUsers || 0, name: '有效VIP' },
            { value: statistics.value?.expiredVipUsers || 0, name: '过期VIP' },
            { value: statistics.value?.nonVipUsers || 0, name: '非VIP' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }))
    
    const vipTypeBarOption = computed(() => ({
      title: {
        text: 'VIP类型分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: ['Premium', 'Basic', 'Enterprise', '非VIP']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '用户数量',
          type: 'bar',
          data: [
            statistics.value?.premiumUsers || 0,
            statistics.value?.basicUsers || 0,
            statistics.value?.enterpriseUsers || 0,
            statistics.value?.nonVipUsers || 0
          ],
          itemStyle: {
            color: function(params) {
              const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666']
              return colors[params.dataIndex]
            }
          }
        }
      ]
    }))
    
    const refreshData = async () => {
      loading.value = true
      try {
        await store.dispatch('fetchStatistics')
        ElMessage.success('统计数据刷新成功')
      } catch (error) {
        console.error('Failed to fetch statistics:', error)
      } finally {
        loading.value = false
      }
    }
    
    onMounted(() => {
      refreshData()
    })
    
    return {
      statistics,
      loading,
      activePercentage,
      vipPercentage,
      validVipPercentage,
      expiredVipPercentage,
      progressColors,
      userStatusChartOption,
      vipStatusChartOption,
      vipTypeBarOption,
      refreshData
    }
  }
}
</script>

<style scoped>
.statistics {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-overview {
  margin-bottom: 20px;
}

.stat-item h3 {
  margin-bottom: 15px;
  color: #333;
  text-align: center;
}

.charts-section {
  margin: 20px 0;
}

.chart {
  height: 300px;
}

.percentage-analysis {
  padding: 20px;
}

.percentage-item {
  margin-bottom: 20px;
}

.percentage-item .label {
  display: inline-block;
  width: 120px;
  font-weight: 500;
  color: #333;
}

.valid-vip {
  color: #67c23a;
  font-weight: bold;
}

.expired-vip {
  color: #f56c6c;
  font-weight: bold;
}
</style>
