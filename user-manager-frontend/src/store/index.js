import { createStore } from 'vuex'
import api from '@/utils/api'

export default createStore({
  state: {
    user: null,
    token: localStorage.getItem('token') || null,
    users: [],
    statistics: null
  },
  
  getters: {
    isAuthenticated: state => !!state.token,
    currentUser: state => state.user,
    allUsers: state => state.users,
    userStatistics: state => state.statistics
  },
  
  mutations: {
    SET_TOKEN(state, token) {
      state.token = token
      if (token) {
        localStorage.setItem('token', token)
      } else {
        localStorage.removeItem('token')
      }
    },
    
    SET_USER(state, user) {
      state.user = user
    },
    
    SET_USERS(state, users) {
      state.users = users
    },
    
    SET_STATISTICS(state, statistics) {
      state.statistics = statistics
    },
    
    LOGOUT(state) {
      state.user = null
      state.token = null
      state.users = []
      state.statistics = null
      localStorage.removeItem('token')
    }
  },
  
  actions: {
    async login({ commit }, credentials) {
      try {
        const response = await api.post('/admin/login', credentials)
        const { token, username, role } = response.data.data
        
        commit('SET_TOKEN', token)
        commit('SET_USER', { username, role })
        
        return response.data
      } catch (error) {
        throw error
      }
    },
    
    async fetchUsers({ commit }) {
      try {
        const response = await api.get('/admin/users')
        commit('SET_USERS', response.data.data)
        return response.data
      } catch (error) {
        throw error
      }
    },
    
    async fetchStatistics({ commit }) {
      try {
        const response = await api.get('/admin/statistics')
        commit('SET_STATISTICS', response.data.data)
        return response.data
      } catch (error) {
        throw error
      }
    },
    
    async deleteUser({ dispatch }, userId) {
      try {
        const response = await api.delete(`/admin/users/${userId}`)
        await dispatch('fetchUsers') // 重新获取用户列表
        await dispatch('fetchStatistics') // 重新获取统计信息
        return response.data
      } catch (error) {
        throw error
      }
    },
    
    logout({ commit }) {
      commit('LOGOUT')
    }
  }
})
