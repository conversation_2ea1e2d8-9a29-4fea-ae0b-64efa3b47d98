# UserManager API 项目总结

## 项目概述

UserManager 是一个基于 Spring Boot 的用户激活码登录管理系统的后端API项目。该项目实现了用户通过激活码登录、用户信息管理、VIP会员系统等核心功能。

## 技术栈

- **框架**: Spring Boot 2.7.18
- **数据库**: H2 (开发环境) / MySQL (生产环境)
- **ORM**: Spring Data JPA + Hibernate
- **安全**: Spring Security + JWT
- **构建工具**: Maven
- **Java版本**: Java 8

## 项目结构

```
src/
├── main/
│   ├── java/com/cet/aug/usermanager/
│   │   ├── UserManagerApplication.java          # 主启动类
│   │   ├── config/
│   │   │   ├── DataInitializer.java            # 数据初始化
│   │   │   └── SecurityConfig.java             # 安全配置
│   │   ├── controller/
│   │   │   ├── HealthController.java           # 健康检查控制器
│   │   │   └── UserController.java             # 用户控制器
│   │   ├── dto/
│   │   │   ├── ApiResponse.java                # 统一响应格式
│   │   │   └── LoginRequest.java               # 登录请求DTO
│   │   ├── entity/
│   │   │   ├── User.java                       # 用户实体
│   │   │   └── VipInfo.java                    # VIP信息实体
│   │   ├── exception/
│   │   │   ├── BusinessException.java          # 业务异常
│   │   │   ├── GlobalExceptionHandler.java     # 全局异常处理
│   │   │   └── UserNotFoundException.java      # 用户未找到异常
│   │   ├── repository/
│   │   │   ├── UserRepository.java             # 用户数据访问
│   │   │   └── VipInfoRepository.java          # VIP信息数据访问
│   │   ├── security/
│   │   │   ├── JwtAuthenticationFilter.java    # JWT认证过滤器
│   │   │   └── JwtTokenProvider.java           # JWT令牌提供者
│   │   └── service/
│   │       └── UserService.java                # 用户服务
│   └── resources/
│       └── application.yml                     # 应用配置
└── test/
    └── java/com/cet/aug/usermanager/
        ├── UserManagerApplicationTests.java    # 应用启动测试
        └── controller/
            └── UserControllerTest.java         # 控制器测试
```

## 核心功能

### 1. 用户激活码登录系统
- **接口**: `POST /api/users/card-login`
- **功能**: 用户通过激活码和代理标识登录
- **特性**: 
  - 自动创建新用户
  - 验证激活码和代理标识
  - 生成JWT令牌
  - 创建默认VIP信息

### 2. 用户信息查询
- **接口**: `POST /api/users/whoami`
- **功能**: 获取当前登录用户信息
- **认证**: 需要JWT令牌

### 3. 用户登出
- **接口**: `POST /api/users/logout`
- **功能**: 用户登出系统
- **认证**: 需要JWT令牌

### 4. 健康检查
- **接口**: `GET /api/health`
- **功能**: 系统健康状态检查
- **访问**: 公开接口

## 数据模型

### User (用户)
- `id`: 主键
- `userId`: 用户唯一标识
- `activationCode`: 激活码
- `agent`: 代理标识
- `status`: 用户状态
- `vip`: VIP信息关联
- `createdAt`: 创建时间
- `lastLoginAt`: 最后登录时间

### VipInfo (VIP信息)
- `id`: 主键
- `expireAt`: 过期时间戳
- `dayScore`: 每日积分额度
- `refreshAt`: 积分刷新时间
- `power`: 权限等级
- `product`: 产品类型
- `score`: 总积分
- `scoreUsed`: 已使用积分

## API响应格式

```json
{
  "code": 0,           // 0表示成功，其他表示错误
  "msg": "success",    // 响应消息
  "data": {}           // 响应数据
}
```

## 安全机制

1. **JWT认证**: 使用JWT令牌进行用户认证
2. **密钥管理**: 配置化的JWT密钥
3. **令牌过期**: 24小时令牌有效期
4. **路径保护**: 基于路径的访问控制

## 测试数据

系统启动时会自动初始化测试数据：
- 激活码1: `test123456789` (premium用户)
- 激活码2: `demo987654321` (basic用户)
- 代理标识: `main`

## 运行方式

### 开发环境
```bash
mvn spring-boot:run
```

### 生产环境
```bash
mvn clean package
java -jar target/UserManager-1.0-SNAPSHOT.jar
```

## 访问地址

- **应用地址**: http://localhost:8080/api
- **健康检查**: http://localhost:8080/api/health
- **H2控制台**: http://localhost:8080/api/h2-console (仅开发环境)

## 配置说明

### 数据库配置
- 开发环境使用H2内存数据库
- 生产环境可配置MySQL数据库

### JWT配置
- 密钥长度: 建议64位以上
- 过期时间: 默认24小时
- 请求头: X-Auth-Token

## 项目特点

1. **完整的RESTful API**: 遵循REST设计原则
2. **统一异常处理**: 全局异常处理机制
3. **数据验证**: 请求参数验证
4. **安全认证**: JWT令牌认证
5. **数据持久化**: JPA + Hibernate
6. **自动化测试**: 单元测试覆盖
7. **配置化管理**: 外部化配置
8. **日志记录**: 详细的日志输出

## 测试验证

项目已通过以下测试：
- ✅ 应用启动测试
- ✅ 健康检查接口测试
- ✅ 激活码登录测试（现有用户）
- ✅ 激活码登录测试（新用户创建）
- ✅ 用户信息查询测试
- ✅ 用户登出测试
- ✅ 参数验证测试

## 部署建议

1. **生产环境配置**:
   - 使用MySQL数据库
   - 配置复杂的JWT密钥
   - 启用HTTPS
   - 配置日志文件

2. **性能优化**:
   - 数据库连接池配置
   - JVM参数调优
   - 缓存策略

3. **监控告警**:
   - 应用性能监控
   - 数据库监控
   - 日志监控

## 总结

UserManager API项目成功实现了用户激活码登录管理系统的所有核心功能，具备完整的用户管理、VIP系统、安全认证等特性。项目代码结构清晰，遵循Spring Boot最佳实践，具有良好的可维护性和扩展性。
