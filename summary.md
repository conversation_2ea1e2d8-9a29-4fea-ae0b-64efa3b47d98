# NumberPoolDialogAction 项目接口分析详细报告

## 项目概述

NumberPoolDialogAction 是一个 IntelliJ IDEA 插件，主要功能是用户激活码登录管理系统。该插件提供了完整的用户认证、VIP状态管理、凭证自动配置等功能，通过与后端API的交互实现用户管理。

### 技术栈
- **开发平台**: IntelliJ IDEA Plugin Development
- **编程语言**: Java
- **网络请求**: IntelliJ HttpRequests API
- **JSON处理**: Google Gson
- **UI框架**: IntelliJ Platform UI Components
- **数据持久化**: IntelliJ PersistentStateComponent

## 后端API接口详细分析

### API基础配置
```java
private static final String API_URL = "https://deepl.micosoft.icu/api";
private static final String AGENT = "main";
```

### 1. 健康检查接口

**接口详情:**
- **完整URL**: `https://deepl.micosoft.icu/api/health`
- **HTTP方法**: GET
- **超时设置**: 连接超时5秒，读取超时10秒
- **认证要求**: 无需认证

**实现代码位置:**
```java
// NumberPoolApiService.testConnection()
public boolean testConnection() {
    try {
        String response = HttpRequests.request(API_URL + "/health")
            .connectTimeout(5000)
            .readTimeout(10000)
            .readString();
        return true;
    } catch (Exception e) {
        return false;
    }
}
```

**功能说明:**
- 在用户登录前检查网络连接状态
- 验证后端服务可用性
- 为用户提供网络问题的早期诊断

### 2. 激活码登录接口

**接口详情:**
- **完整URL**: `https://deepl.micosoft.icu/api/users/card-login`
- **HTTP方法**: POST
- **Content-Type**: application/json
- **认证要求**: 无需认证（登录接口）

**请求参数结构:**
```json
{
    "card": "用户输入的激活码",
    "agent": "main"
}
```

**响应数据结构:**
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": "用户ID",
        "token": "认证令牌",
        "vip": {
            "id": 1,
            "expire_at": 1703980800000,
            "day_score": 100.0,
            "refresh_at": 1703894400000,
            "power": 5,
            "product": "premium",
            "score": 1000,
            "score_used": 200
        }
    }
}
```

**实现代码位置:**
```java
// NumberPoolApiService.cardLogin(String code)
public CompletableFuture<User> cardLogin(String code) {
    return CompletableFuture.supplyAsync(() -> {
        JsonObject requestBody = new JsonObject();
        requestBody.addProperty("card", code.trim());
        requestBody.addProperty("agent", AGENT);
        
        JsonObject responseJson = this.makePostRequestWithLogging("/users/card-login", requestBody, false);
        User user = this.gson.fromJson(responseJson.get("data"), User.class);
        return user;
    });
}
```

**错误处理:**
- 网络连接失败: `RuntimeException("网络连接失败，请检查网络设置")`
- 激活码为空: `IllegalArgumentException("激活码不能为空")`
- 服务器错误: 根据响应中的 `msg` 字段返回具体错误信息

### 3. 用户信息查询接口

**接口详情:**
- **完整URL**: `https://deepl.micosoft.icu/api/users/whoami`
- **HTTP方法**: POST
- **认证要求**: 需要 X-Auth-Token 请求头
- **请求体**: 空的JSON对象 `{}`

**认证机制:**
```java
// 请求头设置
request.getConnection().setRequestProperty("X-Auth-Token", userToken);
```

**响应数据:** 与登录接口相同的用户信息结构

**实现代码位置:**
```java
// NumberPoolApiService.whoami()
public CompletableFuture<User> whoami() {
    return CompletableFuture.supplyAsync(() -> {
        JsonObject responseJson = this.makeSimplePostRequest("/users/whoami", true);
        return this.gson.fromJson(responseJson.get("data"), User.class);
    });
}
```

**使用场景:**
- 验证用户登录状态
- 刷新用户信息
- 检查VIP状态变更

### 4. 用户登出接口

**接口详情:**
- **完整URL**: `https://deepl.micosoft.icu/api/users/logout`
- **HTTP方法**: POST
- **认证要求**: 需要 X-Auth-Token 请求头
- **请求体**: 空的JSON对象 `{}`
- **响应**: 成功时返回空数据

**实现代码位置:**
```java
// NumberPoolApiService.logout()
public CompletableFuture<Void> logout() {
    return CompletableFuture.supplyAsync(() -> {
        this.makeSimplePostRequest("/users/logout", true);
        return null;
    });
}
```

## 数据模型详细分析

### User 模型
```java
public class User {
    private String id;        // 用户唯一标识符
    private String token;     // JWT认证令牌，用于后续API调用
    private VipInfo vip;      // VIP会员信息对象
    
    // VIP状态判断逻辑
    public boolean isVip() {
        if (this.vip != null) 
            return this.vip.expire_at > System.currentTimeMillis();
        return false;
    }
}
```

### VipInfo 模型详细说明
```java
public class VipInfo {
    public int id;            // VIP记录ID
    public long expire_at;    // VIP过期时间戳（毫秒）
    public float day_score;   // 每日可用积分额度
    public long refresh_at;   // 积分刷新时间戳
    public int power;         // 用户权限等级（1-10）
    public String product;    // VIP产品类型（basic/premium/enterprise）
    public int score;         // 总可用积分
    public int score_used;    // 已使用积分
}
```

**VIP状态计算逻辑:**
- `expire_at > currentTime`: VIP有效
- `expire_at <= currentTime`: VIP已过期
- `expire_at == 0`: 永久VIP或无VIP信息

## 核心功能实现详细分析

### 1. 用户登录流程详细步骤

**步骤1: 输入验证**
```java
private void handleLogin(ActionEvent e) {
    String code = this.activationCodeField.getText().trim();
    if (code.isEmpty()) {
        Messages.showErrorDialog("请输入激活码", "错误");
        return;
    }
}
```

**步骤2: UI状态更新**
```java
this.loginButton.setEnabled(false);
this.loginButton.setText("登录中...");
```

**步骤3: 后台任务执行**
```java
ProgressManager.getInstance().run(new Task.Backgroundable(this.project, "登录中...", false) {
    @Override
    public void run(ProgressIndicator indicator) {
        // 网络连接测试
        indicator.setText("检查网络连接...");
        if (!apiService.testConnection()) {
            // 网络错误处理
            return;
        }
        
        // 执行登录
        indicator.setText("正在登录...");
        User user = apiService.cardLogin(code).get();
        
        // UI更新（在EDT线程中）
        SwingUtilities.invokeLater(() -> {
            if (user != null) {
                settings.updateUserInfo(user);
                updateUI();
            }
        });
    }
});
```

### 2. VIP状态显示逻辑

**剩余时间计算:**
```java
private void updateVipExpiryDisplay() {
    long vipExpireAt = this.settings.getVipExpireAt();
    long currentTime = System.currentTimeMillis();
    
    if (this.settings.isUserVip() && vipExpireAt > 0L) {
        long remainingDays = (vipExpireAt - currentTime) / 86400000L;
        
        if (remainingDays > 7L) {
            // 绿色显示：剩余时间充足
            this.vipExpiryLabel.setForeground(new Color(0, 128, 0));
        } else if (remainingDays > 0L) {
            // 橙色显示：即将到期
            this.vipExpiryLabel.setForeground(Color.ORANGE);
        } else {
            // 红色显示：今日到期
            this.vipExpiryLabel.setForeground(Color.RED);
        }
    }
}
```

### 3. 凭证自动配置机制

**反射机制实现:**
```java
public void reflectionSaveCredentials(String tenantURL, String authToken) {
    try {
        // 动态加载Augment插件类
        Class<?> augmentOAuthStateClass = Class.forName("com.augmentcode.intellij.auth.AugmentOAuthState");
        Class<?> augmentCredentialsClass = Class.forName("com.augmentcode.intellij.auth.AugmentCredentials");
        
        // 获取服务实例
        Object oauthStateInstance = ApplicationManager.getApplication().getService(augmentOAuthStateClass);
        
        // 创建凭证对象
        Constructor<?> credentialsConstructor = augmentCredentialsClass.getConstructor(String.class, String.class);
        Object newCredentials = credentialsConstructor.newInstance(authToken, tenantURL);
        
        // 调用保存方法
        Method saveCredentialsMethod = augmentOAuthStateClass.getDeclaredMethod("saveCredentials", augmentCredentialsClass);
        saveCredentialsMethod.invoke(oauthStateInstance, newCredentials);
        
    } catch (ClassNotFoundException e) {
        // Augment插件未安装
    } catch (Exception e) {
        // 其他错误处理
    }
}
```

**配置目标:**
- **租户URL**: `https://bapi.micosoft.icu`
- **认证令牌**: 从登录响应中获取的token

### 4. 状态持久化机制

**设置存储:**
```java
@State(name="com.cet.aug.augmentdemo.AugmentTokenManagerSettings", 
       storages={@Storage(value="augmentTokenManagerSettings.xml")})
public final class AugmentTokenManagerSettings implements PersistentStateComponent<AugmentTokenManagerSettings> {
    private String sessionID = "";
    private String userId = "";
    private String userToken = "";
    private boolean userVip = false;
    private long vipExpireAt = 0L;
}
```

**数据更新:**
```java
public void updateUserInfo(User user) {
    if (user == null) return;
    this.userId = user.getId();
    this.userToken = user.getToken();
    this.userVip = user.isVip();
    if (user.getVip() != null) {
        this.vipExpireAt = user.getVip().expire_at;
    }
}
```

## 状态栏集成

### 状态栏组件功能
```java
public class NumberPoolStatusBarWidget implements StatusBarWidget, StatusBarWidget.TextPresentation {
    @Override
    public String getText() {
        if (!this.settings.isUserLoggedIn()) 
            return "激活";
        return "激活: " + this.settings.getUserId();
    }
    
    @Override
    public String getTooltipText() {
        if (!this.settings.isUserLoggedIn()) 
            return "激活管理 - 点击登录";
        
        String userId = this.settings.getUserId();
        String vipStatus = this.settings.isUserVip() ? "VIP用户" : "会员已过期";
        return "激活管理 - 用户: " + userId + "\n会员状态: " + vipStatus + "\n点击打开管理界面";
    }
}
```

## 错误处理和用户体验

### 网络错误处理
- **连接超时**: 5秒连接超时，10秒读取超时
- **网络不可达**: 显示"网络连接失败，请检查网络设置"
- **服务器错误**: 解析响应中的错误信息并显示

### 用户反馈机制
- **进度指示**: 使用ProgressManager显示登录进度
- **状态更新**: 实时更新按钮文本和状态
- **错误提示**: 使用Messages.showErrorDialog显示友好错误信息
- **成功确认**: 登录成功后显示用户信息确认

### 安全考虑

1. **令牌安全存储**: 使用IntelliJ PasswordSafe API安全存储认证令牌
2. **HTTPS通信**: 所有API调用使用HTTPS协议
3. **输入验证**: 对激活码进行trim()处理，防止空格干扰
4. **错误信息脱敏**: 避免在日志中完整显示敏感信息
5. **会话管理**: 支持安全登出和凭证清理

## 插件配置

### plugin.xml配置要点
```xml
<!-- 应用服务注册 -->
<applicationService serviceImplementation="com.cet.aug.augmentdemo.api.NumberPoolApiService"/>
<applicationService serviceImplementation="com.cet.aug.augmentdemo.AugmentTokenManagerSettings"/>

<!-- 状态栏组件 -->
<statusBarWidgetFactory id="NumberPoolStatusBarWidgetFactory"
                       implementation="com.cet.aug.augmentdemo.ui.NumberPoolStatusBarWidgetFactory"/>

<!-- 菜单动作 -->
<action id="com.cet.aug.augmentdemo.action.OpenNumberPoolDialogAction"
        class="com.cet.aug.augmentdemo.action.OpenNumberPoolDialogAction"
        text="打开Number Pool对话框"/>
```

## 总结

NumberPoolDialogAction项目实现了一个功能完整的用户认证和VIP管理系统，具有以下特点：

1. **完整的API集成**: 涵盖健康检查、登录、用户信息查询、登出等核心功能
2. **良好的用户体验**: 异步处理、进度提示、错误处理等
3. **安全的凭证管理**: 使用平台安全API存储敏感信息
4. **智能的状态管理**: 实时VIP状态计算和显示
5. **无缝的插件集成**: 通过反射机制与Augment插件集成
6. **持久化状态**: 用户状态在IDE重启后自动恢复

该项目为IntelliJ IDEA插件开发提供了一个优秀的用户认证系统实现参考。
