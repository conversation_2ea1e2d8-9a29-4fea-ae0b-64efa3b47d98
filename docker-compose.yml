version: '3.8'

services:

  # 后端服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: usermanager-backend
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE:-prod}
      SPRING_DATASOURCE_URL: *******************************/${MYSQL_DATABASE:-model}?useSSL=false&serverTimezone=UTC&characterEncoding=utf8&autoReconnect=true&failOverReadOnly=false&maxReconnects=10
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: Cll@123?
      JWT_SECRET: ${JWT_SECRET:-mySecretKey1234567890123456789012345678901234567890123456789012345678901234567890}
      SERVER_PORT: 8080
    ports:
      - "${BACKEND_PORT:-8080}:8080"
    volumes:
      - backend_logs:/app/logs
    networks:
      - usermanager-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      timeout: 10s
      retries: 5
      interval: 30s
      start_period: 60s

  # 前端服务
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: usermanager-frontend
    restart: unless-stopped
    ports:
      - "${FRONTEND_PORT:-80}:80"
    networks:
      - usermanager-network
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      timeout: 5s
      retries: 3
      interval: 30s
      start_period: 10s

volumes:
  backend_logs:
    driver: local

networks:
  usermanager-network:
    driver: bridge
