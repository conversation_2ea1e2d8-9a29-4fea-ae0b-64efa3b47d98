#!/bin/bash

# UserManager 一键部署脚本
# 用于在Linux环境中部署UserManager应用

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"
PROJECT_NAME="usermanager"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装 $1"
        exit 1
    fi
}

# 检查系统环境
check_system() {
    log_info "检查系统环境..."
    
    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        log_warning "此脚本主要为Linux系统设计"
    fi
    
    # 检查必要的命令
    check_command "docker"
    check_command "docker-compose"
    
    # 检查Docker服务状态
    if ! systemctl is-active --quiet docker; then
        log_info "启动Docker服务..."
        sudo systemctl start docker
        sudo systemctl enable docker
    fi
    
    # 检查Docker版本
    DOCKER_VERSION=$(docker --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
    log_info "Docker版本: $DOCKER_VERSION"
    
    # 检查Docker Compose版本
    COMPOSE_VERSION=$(docker-compose --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
    log_info "Docker Compose版本: $COMPOSE_VERSION"
    
    log_success "系统环境检查完成"
}

# 创建环境配置文件
create_env_file() {
    log_info "创建环境配置文件..."
    
    if [ ! -f "$ENV_FILE" ]; then
        cat > "$ENV_FILE" << EOF
# MySQL配置
MYSQL_ROOT_PASSWORD=Cll@123?
MYSQL_DATABASE=model
MYSQL_USER=root
MYSQL_PASSWORD=Cll@123?
MYSQL_PORT=3306

# 应用配置
SPRING_PROFILES_ACTIVE=prod
JWT_SECRET=mySecretKey1234567890123456789012345678901234567890123456789012345678901234567890

# 端口配置
FRONTEND_PORT=80
BACKEND_PORT=8080
EOF
        log_success "环境配置文件已创建: $ENV_FILE"
    else
        log_info "环境配置文件已存在: $ENV_FILE"
    fi
}

# 检查必要文件
check_files() {
    log_info "检查必要文件..."
    
    local required_files=(
        "docker-compose.yml"
        "Dockerfile.backend"
        "Dockerfile.frontend"
        "nginx.conf"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "必要文件不存在: $file"
            exit 1
        fi
    done
    
    log_success "必要文件检查完成"
}

# 停止现有服务
stop_services() {
    log_info "停止现有服务..."
    
    if docker-compose -f "$COMPOSE_FILE" ps -q | grep -q .; then
        docker-compose -f "$COMPOSE_FILE" down
        log_info "现有服务已停止"
    else
        log_info "没有运行中的服务"
    fi
}

# 清理Docker资源
cleanup_docker() {
    log_info "清理Docker资源..."
    
    # 清理停止的容器
    if docker ps -a -q --filter "name=$PROJECT_NAME" | grep -q .; then
        docker rm -f $(docker ps -a -q --filter "name=$PROJECT_NAME")
        log_info "已清理停止的容器"
    fi
    
    # 清理未使用的镜像（可选）
    if [ "$1" = "--clean-images" ]; then
        docker image prune -f
        log_info "已清理未使用的镜像"
    fi
    
    # 清理未使用的网络
    docker network prune -f
    
    log_success "Docker资源清理完成"
}

# 构建和启动服务
start_services() {
    log_info "构建和启动服务..."
    
    # 构建镜像并启动服务
    docker-compose -f "$COMPOSE_FILE" up --build -d
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    local max_attempts=60
    local attempt=1
    
    # 等待后端服务就绪
    log_info "等待后端服务..."
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8080/api/health &> /dev/null; then
            log_success "后端服务已就绪"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "后端服务启动超时"
            exit 1
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
    
    # 等待前端服务就绪
    log_info "等待前端服务..."
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost/health &> /dev/null; then
            log_success "前端服务已就绪"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "前端服务启动超时"
            exit 1
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
    
    log_success "所有服务已就绪"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查容器状态
    local containers=$(docker-compose -f "$COMPOSE_FILE" ps --services)
    for container in $containers; do
        local status=$(docker-compose -f "$COMPOSE_FILE" ps -q $container | xargs docker inspect --format='{{.State.Health.Status}}' 2>/dev/null || echo "unknown")
        if [ "$status" = "healthy" ] || [ "$status" = "unknown" ]; then
            log_success "$container: $status"
        else
            log_warning "$container: $status"
        fi
    done
    
    # 检查端口连通性
    local ports=("80" "8080" "3306")
    for port in "${ports[@]}"; do
        if nc -z localhost $port 2>/dev/null; then
            log_success "端口 $port: 可访问"
        else
            log_warning "端口 $port: 不可访问"
        fi
    done
}

# 显示服务信息
show_service_info() {
    log_info "服务信息:"
    echo ""
    echo "  前端地址: http://*************:80"
    echo "  后端API: http://*************:8080/api"
    echo "  MySQL: localhost:3306"
    echo ""
    echo "  查看日志: docker-compose -f $COMPOSE_FILE logs -f [service_name]"
    echo "  停止服务: docker-compose -f $COMPOSE_FILE down"
    echo "  重启服务: docker-compose -f $COMPOSE_FILE restart [service_name]"
    echo ""
}

# 显示帮助信息
show_help() {
    echo "UserManager 一键部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --stop           停止所有服务"
    echo "  --restart        重启所有服务"
    echo "  --status         显示服务状态"
    echo "  --logs           显示服务日志"
    echo "  --clean          清理Docker资源"
    echo "  --clean-all      清理所有Docker资源（包括镜像）"
    echo "  --help           显示此帮助信息"
    echo ""
}

# 主函数
main() {
    case "${1:-deploy}" in
        --stop)
            stop_services
            ;;
        --restart)
            stop_services
            start_services
            wait_for_services
            show_service_info
            ;;
        --status)
            docker-compose -f "$COMPOSE_FILE" ps
            health_check
            ;;
        --logs)
            docker-compose -f "$COMPOSE_FILE" logs -f
            ;;
        --clean)
            stop_services
            cleanup_docker
            ;;
        --clean-all)
            stop_services
            cleanup_docker --clean-images
            ;;
        --help)
            show_help
            ;;
        deploy|*)
            log_info "UserManager 一键部署开始..."
            check_system
            create_env_file
            check_files
            stop_services
            cleanup_docker
            start_services
            wait_for_services
            health_check
            show_service_info
            log_success "UserManager 部署完成！"
            ;;
    esac
}

# 执行主函数
main "$@"
